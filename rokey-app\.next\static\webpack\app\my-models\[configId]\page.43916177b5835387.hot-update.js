"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/[configId]/page",{

/***/ "(app-pages-browser)/./src/app/my-models/[configId]/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/my-models/[configId]/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ConfigDetailsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _config_models__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/config/models */ \"(app-pages-browser)/./src/config/models.ts\");\n/* harmony import */ var _config_roles__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/roles */ \"(app-pages-browser)/./src/config/roles.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowLeftIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudArrowDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/InformationCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckCircleIcon,CloudArrowDownIcon,Cog6ToothIcon,GlobeAltIcon,InformationCircleIcon,KeyIcon,PencilIcon,PlusCircleIcon,PlusIcon,ShieldCheckIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var react_tooltip__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-tooltip */ \"(app-pages-browser)/./node_modules/react-tooltip/dist/react-tooltip.min.mjs\");\n/* harmony import */ var _components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/ConfirmationModal */ \"(app-pages-browser)/./src/components/ui/ConfirmationModal.tsx\");\n/* harmony import */ var _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useConfirmation */ \"(app-pages-browser)/./src/hooks/useConfirmation.ts\");\n/* harmony import */ var _hooks_useManageKeysPrefetch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useManageKeysPrefetch */ \"(app-pages-browser)/./src/hooks/useManageKeysPrefetch.ts\");\n/* harmony import */ var _components_ManageKeysLoadingSkeleton__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ManageKeysLoadingSkeleton */ \"(app-pages-browser)/./src/components/ManageKeysLoadingSkeleton.tsx\");\n/* harmony import */ var _hooks_useRoutingSetupPrefetch__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/useRoutingSetupPrefetch */ \"(app-pages-browser)/./src/hooks/useRoutingSetupPrefetch.ts\");\n/* harmony import */ var _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/contexts/NavigationContext */ \"(app-pages-browser)/./src/contexts/NavigationContext.tsx\");\n/* harmony import */ var _components_UserApiKeys_ApiKeyManager__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/UserApiKeys/ApiKeyManager */ \"(app-pages-browser)/./src/components/UserApiKeys/ApiKeyManager.tsx\");\n/* harmony import */ var _components_TierEnforcement__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/TierEnforcement */ \"(app-pages-browser)/./src/components/TierEnforcement/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n // For accessing route params\n // Ensure path is correct\n\n\n\n\n\n\n\n\n\n\n\n// Updated: PROVIDER_OPTIONS uses p.id (slug) for value and p.name for label\nconst PROVIDER_OPTIONS = _config_models__WEBPACK_IMPORTED_MODULE_3__.llmProviders.map(_c = (p)=>({\n        value: p.id,\n        label: p.name\n    }));\n_c1 = PROVIDER_OPTIONS;\nfunction ConfigDetailsPage() {\n    var _PROVIDER_OPTIONS_, _llmProviders_find;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const configId = params.configId;\n    // Confirmation modal hook\n    const confirmation = (0,_hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_7__.useConfirmation)();\n    // Navigation hook with safe context\n    const navigationContext = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_11__.useNavigationSafe)();\n    const navigateOptimistically = (navigationContext === null || navigationContext === void 0 ? void 0 : navigationContext.navigateOptimistically) || ((href)=>{\n        window.location.href = href;\n    });\n    // Prefetch hooks\n    const { getCachedData, isCached } = (0,_hooks_useManageKeysPrefetch__WEBPACK_IMPORTED_MODULE_8__.useManageKeysPrefetch)();\n    const { createHoverPrefetch: createRoutingHoverPrefetch } = (0,_hooks_useRoutingSetupPrefetch__WEBPACK_IMPORTED_MODULE_10__.useRoutingSetupPrefetch)();\n    const [configDetails, setConfigDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingConfig, setIsLoadingConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showOptimisticLoading, setShowOptimisticLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Provider state now stores the slug (p.id)\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(((_PROVIDER_OPTIONS_ = PROVIDER_OPTIONS[0]) === null || _PROVIDER_OPTIONS_ === void 0 ? void 0 : _PROVIDER_OPTIONS_.value) || 'openai'); // Stores slug\n    const [predefinedModelId, setPredefinedModelId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [apiKeyRaw, setApiKeyRaw] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [label, setLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [temperature, setTemperature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.0);\n    const [isSavingKey, setIsSavingKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State for dynamic model fetching\n    const [fetchedProviderModels, setFetchedProviderModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isFetchingProviderModels, setIsFetchingProviderModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [fetchProviderModelsError, setFetchProviderModelsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [savedKeysWithRoles, setSavedKeysWithRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingKeysAndRoles, setIsLoadingKeysAndRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDeletingKey, setIsDeletingKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [defaultGeneralChatKeyId, setDefaultGeneralChatKeyId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingRolesApiKey, setEditingRolesApiKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State for editing API keys\n    const [editingApiKey, setEditingApiKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editTemperature, setEditTemperature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.0);\n    const [editPredefinedModelId, setEditPredefinedModelId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSavingEdit, setIsSavingEdit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // State for User-Defined Custom Roles\n    const [userCustomRoles, setUserCustomRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingUserCustomRoles, setIsLoadingUserCustomRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userCustomRolesError, setUserCustomRolesError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCreateCustomRoleForm, setShowCreateCustomRoleForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newCustomRoleId, setNewCustomRoleId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newCustomRoleName, setNewCustomRoleName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newCustomRoleDescription, setNewCustomRoleDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSavingCustomRole, setIsSavingCustomRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createCustomRoleError, setCreateCustomRoleError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deletingCustomRoleId, setDeletingCustomRoleId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // stores the DB ID (UUID) of the custom role\n    // State for tab management\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('provider-keys');\n    // Fetch config details with optimistic loading\n    const fetchConfigDetails = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConfigDetailsPage.useCallback[fetchConfigDetails]\": async ()=>{\n            if (!configId) return;\n            // Check for cached data first\n            const cachedData = getCachedData(configId);\n            if (cachedData && cachedData.configDetails) {\n                console.log(\"⚡ [MANAGE KEYS] Using cached config data for: \".concat(configId));\n                setConfigDetails(cachedData.configDetails);\n                setIsLoadingConfig(false);\n                return;\n            }\n            // Show optimistic loading for first-time visits\n            if (!isCached(configId)) {\n                setShowOptimisticLoading(true);\n            }\n            setIsLoadingConfig(true);\n            setError(null);\n            try {\n                const res = await fetch(\"/api/custom-configs\");\n                if (!res.ok) {\n                    const errData = await res.json();\n                    throw new Error(errData.error || 'Failed to fetch configurations list');\n                }\n                const allConfigs = await res.json();\n                const currentConfig = allConfigs.find({\n                    \"ConfigDetailsPage.useCallback[fetchConfigDetails].currentConfig\": (c)=>c.id === configId\n                }[\"ConfigDetailsPage.useCallback[fetchConfigDetails].currentConfig\"]);\n                if (!currentConfig) throw new Error('Configuration not found in the list.');\n                setConfigDetails(currentConfig);\n            } catch (err) {\n                setError(\"Error loading model configuration: \".concat(err.message));\n                setConfigDetails(null);\n            } finally{\n                setIsLoadingConfig(false);\n                setShowOptimisticLoading(false);\n            }\n        }\n    }[\"ConfigDetailsPage.useCallback[fetchConfigDetails]\"], [\n        configId,\n        getCachedData,\n        isCached\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            fetchConfigDetails();\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        fetchConfigDetails\n    ]);\n    // New: Function to fetch all models from the database with caching\n    const fetchModelsFromDatabase = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConfigDetailsPage.useCallback[fetchModelsFromDatabase]\": async ()=>{\n            // Check for cached data first\n            const cachedData = getCachedData(configId);\n            if (cachedData && cachedData.models) {\n                console.log(\"⚡ [MANAGE KEYS] Using cached models data for: \".concat(configId));\n                setFetchedProviderModels(cachedData.models);\n                setIsFetchingProviderModels(false);\n                return;\n            }\n            setIsFetchingProviderModels(true);\n            setFetchProviderModelsError(null);\n            setFetchedProviderModels(null);\n            try {\n                // The new API doesn't need a specific provider or API key in the body to list models\n                const response = await fetch('/api/providers/list-models', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({})\n                });\n                const data = await response.json();\n                if (!response.ok) {\n                    throw new Error(data.error || 'Failed to fetch models from database.');\n                }\n                if (data.models) {\n                    setFetchedProviderModels(data.models);\n                } else {\n                    setFetchedProviderModels([]);\n                }\n            } catch (err) {\n                setFetchProviderModelsError(\"Error fetching models: \".concat(err.message));\n                setFetchedProviderModels([]); // Set to empty array on error to prevent blocking UI\n            } finally{\n                setIsFetchingProviderModels(false);\n            }\n        }\n    }[\"ConfigDetailsPage.useCallback[fetchModelsFromDatabase]\"], [\n        configId,\n        getCachedData\n    ]);\n    // New: Fetch all models from DB when configId is available (i.e., page is ready)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            if (configId) {\n                fetchModelsFromDatabase();\n            }\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        configId,\n        fetchModelsFromDatabase\n    ]);\n    // Updated: Function to fetch all global custom roles for the authenticated user with caching\n    const fetchUserCustomRoles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConfigDetailsPage.useCallback[fetchUserCustomRoles]\": async ()=>{\n            // Check for cached data first\n            const cachedData = getCachedData(configId);\n            if (cachedData && cachedData.userCustomRoles) {\n                console.log(\"⚡ [MANAGE KEYS] Using cached custom roles data for: \".concat(configId));\n                setUserCustomRoles(cachedData.userCustomRoles);\n                setIsLoadingUserCustomRoles(false);\n                return;\n            }\n            setIsLoadingUserCustomRoles(true);\n            setUserCustomRolesError(null);\n            try {\n                const response = await fetch(\"/api/user/custom-roles\"); // New global endpoint\n                if (!response.ok) {\n                    let errorData;\n                    try {\n                        errorData = await response.json(); // Attempt to parse error as JSON\n                    } catch (e) {\n                        // If error response is not JSON, use text or a generic error\n                        const errorText = await response.text().catch({\n                            \"ConfigDetailsPage.useCallback[fetchUserCustomRoles]\": ()=>\"HTTP error \".concat(response.status)\n                        }[\"ConfigDetailsPage.useCallback[fetchUserCustomRoles]\"]);\n                        errorData = {\n                            error: errorText\n                        };\n                    }\n                    const errorMessage = errorData.error || (errorData.issues ? JSON.stringify(errorData.issues) : \"Failed to fetch custom roles (status: \".concat(response.status, \")\"));\n                    if (response.status === 401) {\n                        setUserCustomRolesError(errorMessage);\n                    } else {\n                        throw new Error(errorMessage); // Throw for other errors to be caught by the main catch\n                    }\n                    setUserCustomRoles([]); // Clear roles if there was an error handled here\n                } else {\n                    // Only call .json() here if response.ok and body hasn't been read\n                    const data = await response.json();\n                    setUserCustomRoles(data);\n                // setUserCustomRolesError(null); // Clearing error on success is good, but already done at the start of try\n                }\n            } catch (err) {\n                // This catch handles network errors from fetch() or errors thrown from !response.ok block\n                setUserCustomRolesError(err.message);\n                setUserCustomRoles([]); // Clear roles on error\n            } finally{\n                setIsLoadingUserCustomRoles(false);\n            }\n        }\n    }[\"ConfigDetailsPage.useCallback[fetchUserCustomRoles]\"], []);\n    // Fetch API keys and their roles for this config with optimistic loading\n    const fetchKeysAndRolesForConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\": async ()=>{\n            if (!configId || !userCustomRoles) return; // Also wait for userCustomRoles to be available\n            // Check for cached data first\n            const cachedData = getCachedData(configId);\n            if (cachedData && cachedData.apiKeys && cachedData.defaultChatKeyId !== undefined) {\n                console.log(\"⚡ [MANAGE KEYS] Using cached keys data for: \".concat(configId));\n                // Process cached keys with roles (same logic as below)\n                const keysWithRolesPromises = cachedData.apiKeys.map({\n                    \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\": async (key)=>{\n                        const rolesResponse = await fetch(\"/api/keys/\".concat(key.id, \"/roles\"));\n                        let assigned_roles = [];\n                        if (rolesResponse.ok) {\n                            const roleAssignments = await rolesResponse.json();\n                            assigned_roles = roleAssignments.map({\n                                \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\": (ra)=>{\n                                    const predefinedRole = (0,_config_roles__WEBPACK_IMPORTED_MODULE_4__.getRoleById)(ra.role_name);\n                                    if (predefinedRole) return predefinedRole;\n                                    const customRole = userCustomRoles.find({\n                                        \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises.customRole\": (cr)=>cr.role_id === ra.role_name\n                                    }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises.customRole\"]);\n                                    if (customRole) {\n                                        return {\n                                            id: customRole.role_id,\n                                            name: customRole.name,\n                                            description: customRole.description || undefined\n                                        };\n                                    }\n                                    return null;\n                                }\n                            }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\"]).filter(Boolean);\n                        }\n                        return {\n                            ...key,\n                            assigned_roles,\n                            is_default_general_chat_model: cachedData.defaultChatKeyId === key.id\n                        };\n                    }\n                }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\"]);\n                const resolvedKeysWithRoles = await Promise.all(keysWithRolesPromises);\n                setSavedKeysWithRoles(resolvedKeysWithRoles);\n                setDefaultGeneralChatKeyId(cachedData.defaultChatKeyId);\n                setIsLoadingKeysAndRoles(false);\n                return;\n            }\n            setIsLoadingKeysAndRoles(true);\n            // Preserve config loading errors, clear others\n            setError({\n                \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\": (prev)=>prev && prev.startsWith('Error loading model configuration:') ? prev : null\n            }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\"]);\n            setSuccessMessage(null);\n            try {\n                // Fetch all keys for the config\n                const keysResponse = await fetch(\"/api/keys?custom_config_id=\".concat(configId));\n                if (!keysResponse.ok) {\n                    const errorData = await keysResponse.json();\n                    throw new Error(errorData.error || 'Failed to fetch API keys');\n                }\n                const keys = await keysResponse.json();\n                // Fetch default general chat key\n                const defaultKeyResponse = await fetch(\"/api/custom-configs/\".concat(configId, \"/default-chat-key\"));\n                if (!defaultKeyResponse.ok) {\n                    console.warn('Failed to fetch default chat key info');\n                }\n                const defaultKeyData = defaultKeyResponse.status === 200 ? await defaultKeyResponse.json() : null;\n                setDefaultGeneralChatKeyId((defaultKeyData === null || defaultKeyData === void 0 ? void 0 : defaultKeyData.id) || null);\n                // For each key, fetch its assigned roles\n                const keysWithRolesPromises = keys.map({\n                    \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\": async (key)=>{\n                        const rolesResponse = await fetch(\"/api/keys/\".concat(key.id, \"/roles\"));\n                        let assigned_roles = [];\n                        if (rolesResponse.ok) {\n                            const roleAssignments = await rolesResponse.json();\n                            assigned_roles = roleAssignments.map({\n                                \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\": (ra)=>{\n                                    // 1. Check predefined roles\n                                    const predefinedRole = (0,_config_roles__WEBPACK_IMPORTED_MODULE_4__.getRoleById)(ra.role_name);\n                                    if (predefinedRole) return predefinedRole;\n                                    // 2. Check current user's global custom roles\n                                    const customRole = userCustomRoles.find({\n                                        \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises.customRole\": (cr)=>cr.role_id === ra.role_name\n                                    }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises.customRole\"]);\n                                    if (customRole) {\n                                        return {\n                                            id: customRole.role_id,\n                                            name: customRole.name,\n                                            description: customRole.description || undefined\n                                        };\n                                    }\n                                    // 3. If not found in either, it's a lingering assignment to a deleted/invalid role, so filter it out\n                                    return null;\n                                }\n                            }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\"]).filter(Boolean); // filter(Boolean) removes null entries\n                        }\n                        return {\n                            ...key,\n                            assigned_roles,\n                            is_default_general_chat_model: (defaultKeyData === null || defaultKeyData === void 0 ? void 0 : defaultKeyData.id) === key.id\n                        };\n                    }\n                }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig].keysWithRolesPromises\"]);\n                const resolvedKeysWithRoles = await Promise.all(keysWithRolesPromises);\n                setSavedKeysWithRoles(resolvedKeysWithRoles);\n            } catch (err) {\n                setError({\n                    \"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\": (prev)=>prev ? \"\".concat(prev, \"; \").concat(err.message) : err.message\n                }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\"]); // Append if there was a config load error\n            } finally{\n                setIsLoadingKeysAndRoles(false);\n            }\n        }\n    }[\"ConfigDetailsPage.useCallback[fetchKeysAndRolesForConfig]\"], [\n        configId,\n        userCustomRoles\n    ]); // Added userCustomRoles to dependency array\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            if (configDetails) {\n                fetchUserCustomRoles(); // Call to fetch custom roles\n            }\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        configDetails,\n        fetchUserCustomRoles\n    ]); // Only depends on configDetails and the stable fetchUserCustomRoles\n    // New useEffect to fetch keys and roles when configDetails AND userCustomRoles (state) are ready\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            // Ensure userCustomRoles is not in its initial undefined/null state from useState([])\n            // and actually contains data (or an empty array confirming fetch completion)\n            if (configDetails && userCustomRoles) {\n                fetchKeysAndRolesForConfig();\n            }\n        // This effect runs if configDetails changes, userCustomRoles (state) changes,\n        // or fetchKeysAndRolesForConfig function identity changes (which happens if userCustomRoles state changes).\n        // This is the desired behavior: re-fetch keys/roles if custom roles change.\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        configDetails,\n        userCustomRoles,\n        fetchKeysAndRolesForConfig\n    ]);\n    // Updated: Memoize model options based on selected provider and fetched models\n    const modelOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ConfigDetailsPage.useMemo[modelOptions]\": ()=>{\n            if (fetchedProviderModels) {\n                const currentProviderDetails = _config_models__WEBPACK_IMPORTED_MODULE_3__.llmProviders.find({\n                    \"ConfigDetailsPage.useMemo[modelOptions].currentProviderDetails\": (p)=>p.id === provider\n                }[\"ConfigDetailsPage.useMemo[modelOptions].currentProviderDetails\"]);\n                if (!currentProviderDetails) return [];\n                // If the selected provider is \"OpenRouter\", show all fetched models\n                // as an OpenRouter key can access any of them.\n                if (currentProviderDetails.id === \"openrouter\") {\n                    return fetchedProviderModels.map({\n                        \"ConfigDetailsPage.useMemo[modelOptions]\": (m)=>({\n                                value: m.id,\n                                label: m.display_name || m.name,\n                                provider_id: m.provider_id\n                            })\n                    }[\"ConfigDetailsPage.useMemo[modelOptions]\"]).sort({\n                        \"ConfigDetailsPage.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"ConfigDetailsPage.useMemo[modelOptions]\"]);\n                }\n                // Custom logic for DeepSeek\n                if (currentProviderDetails.id === \"deepseek\") {\n                    console.log('[DeepSeek Debug] Provider is DeepSeek. Fetched models:', JSON.stringify(fetchedProviderModels));\n                    const deepseekOptions = [];\n                    const deepseekChatModel = fetchedProviderModels.find({\n                        \"ConfigDetailsPage.useMemo[modelOptions].deepseekChatModel\": (model)=>model.id === \"deepseek-chat\" && model.provider_id === \"deepseek\"\n                    }[\"ConfigDetailsPage.useMemo[modelOptions].deepseekChatModel\"]);\n                    console.log('[DeepSeek Debug] Found deepseek-chat model:', JSON.stringify(deepseekChatModel));\n                    if (deepseekChatModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-chat\",\n                            label: \"Deepseek V3\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    const deepseekReasonerModel = fetchedProviderModels.find({\n                        \"ConfigDetailsPage.useMemo[modelOptions].deepseekReasonerModel\": (model)=>model.id === \"deepseek-reasoner\" && model.provider_id === \"deepseek\"\n                    }[\"ConfigDetailsPage.useMemo[modelOptions].deepseekReasonerModel\"]);\n                    console.log('[DeepSeek Debug] Found deepseek-reasoner model:', JSON.stringify(deepseekReasonerModel));\n                    if (deepseekReasonerModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-reasoner\",\n                            label: \"DeepSeek R1-0528\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    // If for some reason the specific models are not found in fetchedProviderModels,\n                    // it's better to return an empty array or a message than all DeepSeek models unfiltered.\n                    // Or, as a fallback, show all models for DeepSeek if the specific ones aren't present.\n                    // For now, strictly showing only these two if found.\n                    return deepseekOptions.sort({\n                        \"ConfigDetailsPage.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"ConfigDetailsPage.useMemo[modelOptions]\"]);\n                }\n                // For other providers, filter by their specific provider_id\n                return fetchedProviderModels.filter({\n                    \"ConfigDetailsPage.useMemo[modelOptions]\": (model)=>model.provider_id === currentProviderDetails.id\n                }[\"ConfigDetailsPage.useMemo[modelOptions]\"]).map({\n                    \"ConfigDetailsPage.useMemo[modelOptions]\": (m)=>({\n                            value: m.id,\n                            label: m.display_name || m.name,\n                            provider_id: m.provider_id\n                        })\n                }[\"ConfigDetailsPage.useMemo[modelOptions]\"]).sort({\n                    \"ConfigDetailsPage.useMemo[modelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                }[\"ConfigDetailsPage.useMemo[modelOptions]\"]);\n            }\n            return []; // Return empty array if models haven't been fetched or if fetch failed.\n        }\n    }[\"ConfigDetailsPage.useMemo[modelOptions]\"], [\n        fetchedProviderModels,\n        provider\n    ]);\n    // Model options for edit modal - filtered by the current key's provider\n    const editModelOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ConfigDetailsPage.useMemo[editModelOptions]\": ()=>{\n            if (fetchedProviderModels && editingApiKey) {\n                const currentProviderDetails = _config_models__WEBPACK_IMPORTED_MODULE_3__.llmProviders.find({\n                    \"ConfigDetailsPage.useMemo[editModelOptions].currentProviderDetails\": (p)=>p.id === editingApiKey.provider\n                }[\"ConfigDetailsPage.useMemo[editModelOptions].currentProviderDetails\"]);\n                if (!currentProviderDetails) return [];\n                // If the provider is \"OpenRouter\", show all fetched models\n                if (currentProviderDetails.id === \"openrouter\") {\n                    return fetchedProviderModels.map({\n                        \"ConfigDetailsPage.useMemo[editModelOptions]\": (m)=>({\n                                value: m.id,\n                                label: m.display_name || m.name,\n                                provider_id: m.provider_id\n                            })\n                    }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]).sort({\n                        \"ConfigDetailsPage.useMemo[editModelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]);\n                }\n                // Custom logic for DeepSeek\n                if (currentProviderDetails.id === \"deepseek\") {\n                    const deepseekOptions = [];\n                    const deepseekChatModel = fetchedProviderModels.find({\n                        \"ConfigDetailsPage.useMemo[editModelOptions].deepseekChatModel\": (model)=>model.id === \"deepseek-chat\" && model.provider_id === \"deepseek\"\n                    }[\"ConfigDetailsPage.useMemo[editModelOptions].deepseekChatModel\"]);\n                    if (deepseekChatModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-chat\",\n                            label: \"Deepseek V3\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    const deepseekReasonerModel = fetchedProviderModels.find({\n                        \"ConfigDetailsPage.useMemo[editModelOptions].deepseekReasonerModel\": (model)=>model.id === \"deepseek-reasoner\" && model.provider_id === \"deepseek\"\n                    }[\"ConfigDetailsPage.useMemo[editModelOptions].deepseekReasonerModel\"]);\n                    if (deepseekReasonerModel) {\n                        deepseekOptions.push({\n                            value: \"deepseek-reasoner\",\n                            label: \"DeepSeek R1-0528\",\n                            provider_id: \"deepseek\"\n                        });\n                    }\n                    return deepseekOptions.sort({\n                        \"ConfigDetailsPage.useMemo[editModelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                    }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]);\n                }\n                // For other providers, filter by their specific provider_id\n                return fetchedProviderModels.filter({\n                    \"ConfigDetailsPage.useMemo[editModelOptions]\": (model)=>model.provider_id === currentProviderDetails.id\n                }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]).map({\n                    \"ConfigDetailsPage.useMemo[editModelOptions]\": (m)=>({\n                            value: m.id,\n                            label: m.display_name || m.name,\n                            provider_id: m.provider_id\n                        })\n                }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]).sort({\n                    \"ConfigDetailsPage.useMemo[editModelOptions]\": (a, b)=>(a.label || '').localeCompare(b.label || '')\n                }[\"ConfigDetailsPage.useMemo[editModelOptions]\"]);\n            }\n            return [];\n        }\n    }[\"ConfigDetailsPage.useMemo[editModelOptions]\"], [\n        fetchedProviderModels,\n        editingApiKey\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            // Auto-select the first model from the dynamic modelOptions when provider changes or models load\n            if (modelOptions.length > 0) {\n                setPredefinedModelId(modelOptions[0].value);\n            } else {\n                setPredefinedModelId(''); // Clear if no models for provider\n            }\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        modelOptions,\n        provider\n    ]); // Now depends on modelOptions, which depends on fetchedProviderModels and provider\n    // Fetch models based on the provider's slug (p.id)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfigDetailsPage.useEffect\": ()=>{\n            if (provider) {\n                // Logic to fetch models for the selected provider slug might need adjustment\n                // if it was previously relying on the provider display name.\n                // Assuming fetchProviderModels is adapted or already uses slugs.\n                fetchModelsFromDatabase();\n            }\n        }\n    }[\"ConfigDetailsPage.useEffect\"], [\n        provider,\n        fetchModelsFromDatabase\n    ]);\n    const handleSaveKey = async (e)=>{\n        e.preventDefault();\n        if (!configId) {\n            setError('Configuration ID is missing.');\n            return;\n        }\n        // Frontend validation: Check for duplicate models\n        const isDuplicateModel = savedKeysWithRoles.some((key)=>key.predefined_model_id === predefinedModelId);\n        if (isDuplicateModel) {\n            setError('This model is already configured in this setup. Each model can only be used once per configuration, but you can use the same API key with different models.');\n            return;\n        }\n        setIsSavingKey(true);\n        setError(null);\n        setSuccessMessage(null);\n        // provider state variable already holds the slug\n        const newKeyData = {\n            custom_api_config_id: configId,\n            provider,\n            predefined_model_id: predefinedModelId,\n            api_key_raw: apiKeyRaw,\n            label,\n            temperature\n        };\n        // Store previous state for rollback on error\n        const previousKeysState = [\n            ...savedKeysWithRoles\n        ];\n        try {\n            var _PROVIDER_OPTIONS_;\n            const response = await fetch('/api/keys', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(newKeyData)\n            });\n            const result = await response.json();\n            if (!response.ok) throw new Error(result.details || result.error || 'Failed to save API key');\n            // Create optimistic key object with the returned data\n            const newKey = {\n                id: result.id,\n                custom_api_config_id: configId,\n                provider,\n                predefined_model_id: predefinedModelId,\n                label,\n                temperature,\n                status: 'active',\n                created_at: new Date().toISOString(),\n                last_used_at: null,\n                is_default_general_chat_model: false,\n                assigned_roles: []\n            };\n            // Optimistically add the new key to the list\n            setSavedKeysWithRoles((prevKeys)=>[\n                    ...prevKeys,\n                    newKey\n                ]);\n            setSuccessMessage('API key \"'.concat(label, '\" saved successfully!'));\n            setProvider(((_PROVIDER_OPTIONS_ = PROVIDER_OPTIONS[0]) === null || _PROVIDER_OPTIONS_ === void 0 ? void 0 : _PROVIDER_OPTIONS_.value) || 'openai');\n            setApiKeyRaw('');\n            setLabel('');\n            setTemperature(1.0);\n            // Reset model selection to first available option\n            if (modelOptions.length > 0) {\n                setPredefinedModelId(modelOptions[0].value);\n            }\n        } catch (err) {\n            // Revert UI on error\n            setSavedKeysWithRoles(previousKeysState);\n            setError(\"Save Key Error: \".concat(err.message));\n        } finally{\n            setIsSavingKey(false);\n        }\n    };\n    const handleEditKey = (key)=>{\n        setEditingApiKey(key);\n        setEditTemperature(key.temperature || 1.0);\n        setEditPredefinedModelId(key.predefined_model_id);\n    };\n    const handleSaveEdit = async ()=>{\n        if (!editingApiKey) return;\n        // Frontend validation: Check for duplicate models (excluding the current key being edited)\n        const isDuplicateModel = savedKeysWithRoles.some((key)=>key.id !== editingApiKey.id && key.predefined_model_id === editPredefinedModelId);\n        if (isDuplicateModel) {\n            setError('This model is already configured in this setup. Each model can only be used once per configuration.');\n            return;\n        }\n        setIsSavingEdit(true);\n        setError(null);\n        setSuccessMessage(null);\n        // Store previous state for rollback on error\n        const previousKeysState = [\n            ...savedKeysWithRoles\n        ];\n        // Optimistic UI update\n        setSavedKeysWithRoles((prevKeys)=>prevKeys.map((key)=>{\n                if (key.id === editingApiKey.id) {\n                    return {\n                        ...key,\n                        temperature: editTemperature,\n                        predefined_model_id: editPredefinedModelId\n                    };\n                }\n                return key;\n            }));\n        try {\n            const response = await fetch(\"/api/keys?id=\".concat(editingApiKey.id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    temperature: editTemperature,\n                    predefined_model_id: editPredefinedModelId\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                // Revert UI on error\n                setSavedKeysWithRoles(previousKeysState);\n                throw new Error(result.details || result.error || 'Failed to update API key');\n            }\n            setSuccessMessage('API key \"'.concat(editingApiKey.label, '\" updated successfully!'));\n            setEditingApiKey(null);\n        } catch (err) {\n            setError(\"Update Key Error: \".concat(err.message));\n        } finally{\n            setIsSavingEdit(false);\n        }\n    };\n    const handleDeleteKey = (keyId, keyLabel)=>{\n        confirmation.showConfirmation({\n            title: 'Delete API Key',\n            message: 'Are you sure you want to delete the API key \"'.concat(keyLabel, '\"? This will permanently remove the key and unassign all its roles. This action cannot be undone.'),\n            confirmText: 'Delete API Key',\n            cancelText: 'Cancel',\n            type: 'danger'\n        }, async ()=>{\n            setIsDeletingKey(keyId);\n            setError(null);\n            setSuccessMessage(null);\n            // Store previous state for rollback on error\n            const previousKeysState = [\n                ...savedKeysWithRoles\n            ];\n            const previousDefaultKeyId = defaultGeneralChatKeyId;\n            const keyToDelete = savedKeysWithRoles.find((key)=>key.id === keyId);\n            // Optimistic UI update - immediately remove the key from the list\n            setSavedKeysWithRoles((prevKeys)=>prevKeys.filter((key)=>key.id !== keyId));\n            // If the deleted key was the default, clear the default\n            if (keyToDelete === null || keyToDelete === void 0 ? void 0 : keyToDelete.is_default_general_chat_model) {\n                setDefaultGeneralChatKeyId(null);\n            }\n            try {\n                const response = await fetch(\"/api/keys/\".concat(keyId), {\n                    method: 'DELETE'\n                });\n                const result = await response.json();\n                if (!response.ok) {\n                    // Revert UI on error\n                    setSavedKeysWithRoles(previousKeysState);\n                    setDefaultGeneralChatKeyId(previousDefaultKeyId);\n                    // Special handling for 404 errors (key already deleted)\n                    if (response.status === 404) {\n                        // Key was already deleted, so the optimistic update was correct\n                        // Don't revert the UI, just show a different message\n                        setSavedKeysWithRoles((prevKeys)=>prevKeys.filter((key)=>key.id !== keyId));\n                        setSuccessMessage('API key \"'.concat(keyLabel, '\" was already deleted.'));\n                        return; // Don't throw error\n                    }\n                    throw new Error(result.details || result.error || 'Failed to delete API key');\n                }\n                setSuccessMessage('API key \"'.concat(keyLabel, '\" deleted successfully!'));\n            } catch (err) {\n                setError(\"Delete Key Error: \".concat(err.message));\n                throw err; // Re-throw to keep modal open on error\n            } finally{\n                setIsDeletingKey(null);\n            }\n        });\n    };\n    const handleSetDefaultChatKey = async (apiKeyIdToSet)=>{\n        if (!configId) return;\n        setError(null);\n        setSuccessMessage(null);\n        const previousKeysState = [\n            ...savedKeysWithRoles\n        ]; // Keep a copy in case of error\n        const previousDefaultKeyId = defaultGeneralChatKeyId;\n        // Optimistic UI update\n        setSavedKeysWithRoles((prevKeys)=>prevKeys.map((key)=>({\n                    ...key,\n                    is_default_general_chat_model: key.id === apiKeyIdToSet\n                })));\n        setDefaultGeneralChatKeyId(apiKeyIdToSet); // Update the separate state for default ID\n        try {\n            const response = await fetch(\"/api/custom-configs/\".concat(configId, \"/default-key-handler/\").concat(apiKeyIdToSet), {\n                method: 'PUT'\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                // Revert UI on error\n                setSavedKeysWithRoles(previousKeysState.map((k)=>({\n                        ...k\n                    }))); // Ensure deep copy for re-render\n                setDefaultGeneralChatKeyId(previousDefaultKeyId);\n                throw new Error(result.details || result.error || 'Failed to set default chat key');\n            }\n            setSuccessMessage(result.message || 'Default general chat key updated!');\n        } catch (err) {\n            setError(\"Set Default Error: \".concat(err.message));\n        }\n    };\n    const handleRoleToggle = async (apiKey, roleId, isAssigned)=>{\n        setError(null);\n        setSuccessMessage(null);\n        const endpoint = \"/api/keys/\".concat(apiKey.id, \"/roles\");\n        // For optimistic update, find role details from combined list (predefined or user's global custom roles)\n        const allAvailableRoles = [\n            ..._config_roles__WEBPACK_IMPORTED_MODULE_4__.PREDEFINED_ROLES.map((r)=>({\n                    ...r,\n                    isCustom: false\n                })),\n            ...userCustomRoles.map((cr)=>({\n                    id: cr.role_id,\n                    name: cr.name,\n                    description: cr.description || undefined,\n                    isCustom: true,\n                    databaseId: cr.id\n                }))\n        ];\n        const roleDetails = allAvailableRoles.find((r)=>r.id === roleId) || {\n            id: roleId,\n            name: roleId,\n            description: ''\n        };\n        const previousKeysState = savedKeysWithRoles.map((k)=>({\n                ...k,\n                assigned_roles: [\n                    ...k.assigned_roles.map((r)=>({\n                            ...r\n                        }))\n                ]\n            })); // Deep copy\n        let previousEditingRolesApiKey = null;\n        if (editingRolesApiKey && editingRolesApiKey.id === apiKey.id) {\n            previousEditingRolesApiKey = {\n                ...editingRolesApiKey,\n                assigned_roles: [\n                    ...editingRolesApiKey.assigned_roles.map((r)=>({\n                            ...r\n                        }))\n                ]\n            };\n        }\n        // Optimistic UI update\n        setSavedKeysWithRoles((prevKeys)=>prevKeys.map((key)=>{\n                if (key.id === apiKey.id) {\n                    const updatedRoles = isAssigned ? key.assigned_roles.filter((r)=>r.id !== roleId) : [\n                        ...key.assigned_roles,\n                        roleDetails\n                    ];\n                    return {\n                        ...key,\n                        assigned_roles: updatedRoles\n                    };\n                }\n                return key;\n            }));\n        if (editingRolesApiKey && editingRolesApiKey.id === apiKey.id) {\n            setEditingRolesApiKey((prevEditingKey)=>{\n                if (!prevEditingKey) return null;\n                const updatedRoles = isAssigned ? prevEditingKey.assigned_roles.filter((r)=>r.id !== roleId) : [\n                    ...prevEditingKey.assigned_roles,\n                    roleDetails\n                ];\n                return {\n                    ...prevEditingKey,\n                    assigned_roles: updatedRoles\n                };\n            });\n        }\n        try {\n            let response;\n            if (isAssigned) {\n                response = await fetch(\"\".concat(endpoint, \"/\").concat(roleId), {\n                    method: 'DELETE'\n                });\n            } else {\n                response = await fetch(endpoint, {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        role_name: roleId\n                    })\n                });\n            }\n            const result = await response.json();\n            if (!response.ok) {\n                // Revert UI on error\n                setSavedKeysWithRoles(previousKeysState);\n                if (previousEditingRolesApiKey) {\n                    setEditingRolesApiKey(previousEditingRolesApiKey);\n                } else if (editingRolesApiKey && editingRolesApiKey.id === apiKey.id) {\n                    const originalKeyData = previousKeysState.find((k)=>k.id === apiKey.id);\n                    if (originalKeyData) setEditingRolesApiKey(originalKeyData);\n                }\n                // Use the error message from the backend if available (e.g., for 409 conflict)\n                const errorMessage = response.status === 409 && result.error ? result.error : result.details || result.error || (isAssigned ? 'Failed to unassign role' : 'Failed to assign role');\n                throw new Error(errorMessage);\n            }\n            setSuccessMessage(result.message || \"Role '\".concat(roleDetails.name, \"' \").concat(isAssigned ? 'unassigned' : 'assigned', \" successfully.\"));\n        } catch (err) {\n            // err.message now contains the potentially more user-friendly message from the backend or a fallback\n            setError(\"Role Update Error: \".concat(err.message));\n        }\n    };\n    const handleCreateCustomRole = async ()=>{\n        // Removed editingRolesApiKey check as creating a global role isn't tied to a specific key being edited.\n        // configId is also not needed for creating a global role.\n        if (!newCustomRoleId.trim() || newCustomRoleId.trim().length > 30 || !/^[a-zA-Z0-9_]+$/.test(newCustomRoleId.trim())) {\n            setCreateCustomRoleError('Role ID is required (max 30 chars, letters, numbers, underscores only).');\n            return;\n        }\n        // Check against PREDEFINED_ROLES and the user's existing global custom roles\n        if (_config_roles__WEBPACK_IMPORTED_MODULE_4__.PREDEFINED_ROLES.some((pr)=>pr.id.toLowerCase() === newCustomRoleId.trim().toLowerCase()) || userCustomRoles.some((cr)=>cr.role_id.toLowerCase() === newCustomRoleId.trim().toLowerCase())) {\n            setCreateCustomRoleError('This Role ID is already in use (either predefined or as one of your custom roles).');\n            return;\n        }\n        if (!newCustomRoleName.trim()) {\n            setCreateCustomRoleError('Role Name is required.');\n            return;\n        }\n        setCreateCustomRoleError(null);\n        setIsSavingCustomRole(true);\n        try {\n            const response = await fetch(\"/api/user/custom-roles\", {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    role_id: newCustomRoleId.trim(),\n                    name: newCustomRoleName.trim(),\n                    description: newCustomRoleDescription.trim()\n                })\n            });\n            if (!response.ok) {\n                // Try to parse the error response as JSON, but fallback if it's not JSON\n                let errorResult;\n                try {\n                    errorResult = await response.json();\n                } catch (parseError) {\n                    // If JSON parsing fails, use the response text or a generic status message\n                    const errorText = await response.text().catch(()=>\"HTTP status \".concat(response.status));\n                    errorResult = {\n                        error: \"Server error, could not parse response.\",\n                        details: errorText\n                    };\n                }\n                let displayError = errorResult.error || 'Failed to create custom role.';\n                if (errorResult.details) {\n                    displayError += \" (Details: \".concat(errorResult.details, \")\");\n                } else if (errorResult.issues) {\n                    // If Zod issues, format them for better readability\n                    const issuesString = Object.entries(errorResult.issues).map((param)=>{\n                        let [field, messages] = param;\n                        return \"\".concat(field, \": \").concat(messages.join(', '));\n                    }).join('; ');\n                    displayError += \" (Issues: \".concat(issuesString, \")\");\n                }\n                throw new Error(displayError);\n            }\n            // If response IS ok, then parse the successful JSON response\n            const result = await response.json();\n            setNewCustomRoleId('');\n            setNewCustomRoleName('');\n            setNewCustomRoleDescription('');\n            // setShowCreateCustomRoleForm(false); // User might want to add multiple roles\n            fetchUserCustomRoles(); // Refresh the global list\n            setSuccessMessage(\"Custom role '\".concat(result.name, \"' created successfully! It is now available globally.\"));\n        } catch (err) {\n            setCreateCustomRoleError(err.message);\n        } finally{\n            setIsSavingCustomRole(false);\n        }\n    };\n    const handleDeleteCustomRole = (customRoleDatabaseId, customRoleName)=>{\n        // configId is not needed for deleting a global role\n        if (!customRoleDatabaseId) return;\n        confirmation.showConfirmation({\n            title: 'Delete Custom Role',\n            message: 'Are you sure you want to delete the custom role \"'.concat(customRoleName, \"\\\"? This will unassign it from all API keys where it's currently used. This action cannot be undone.\"),\n            confirmText: 'Delete Role',\n            cancelText: 'Cancel',\n            type: 'danger'\n        }, async ()=>{\n            setDeletingCustomRoleId(customRoleDatabaseId);\n            setUserCustomRolesError(null);\n            setCreateCustomRoleError(null);\n            setSuccessMessage(null);\n            try {\n                const response = await fetch(\"/api/user/custom-roles/\".concat(customRoleDatabaseId), {\n                    method: 'DELETE'\n                });\n                const result = await response.json(); // Try to parse JSON for all responses\n                if (!response.ok) {\n                    throw new Error(result.error || 'Failed to delete custom role');\n                }\n                // Optimistically remove from the local state\n                setUserCustomRoles((prev)=>prev.filter((role)=>role.id !== customRoleDatabaseId));\n                setSuccessMessage(result.message || 'Global custom role \"'.concat(customRoleName, '\" deleted successfully.'));\n                // Re-fetch keys and roles for the current config, as the deleted global role might have been assigned here.\n                // This ensures the displayed assigned roles for keys on this page are up-to-date.\n                if (configId) {\n                    fetchKeysAndRolesForConfig();\n                }\n            } catch (err) {\n                setUserCustomRolesError(\"Error deleting role: \".concat(err.message));\n                throw err; // Re-throw to keep modal open on error\n            } finally{\n                setDeletingCustomRoleId(null);\n            }\n        });\n    };\n    const renderManageRolesModal = ()=>{\n        if (!editingRolesApiKey) return null;\n        const combinedRoles = [\n            ..._config_roles__WEBPACK_IMPORTED_MODULE_4__.PREDEFINED_ROLES.map((r)=>({\n                    ...r,\n                    isCustom: false\n                })),\n            ...userCustomRoles.map((cr)=>({\n                    id: cr.role_id,\n                    name: cr.name,\n                    description: cr.description || undefined,\n                    isCustom: true,\n                    databaseId: cr.id // The actual DB ID (UUID) for delete operations\n                }))\n        ].sort((a, b)=>a.name.localeCompare(b.name));\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card w-full max-w-lg max-h-[90vh] flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center p-6 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900\",\n                                children: [\n                                    \"Manage Roles for: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-orange-600\",\n                                        children: editingRolesApiKey.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 952,\n                                        columnNumber: 83\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 952,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setEditingRolesApiKey(null);\n                                    setShowCreateCustomRoleForm(false);\n                                    setCreateCustomRoleError(null);\n                                },\n                                className: \"text-gray-500 hover:text-gray-700 hover:bg-gray-100 p-1 rounded-lg transition-colors duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 954,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 953,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 951,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b border-gray-200\",\n                        children: [\n                            userCustomRolesError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 border border-red-200 rounded-lg p-3 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-800 text-sm\",\n                                    children: [\n                                        \"Error with custom roles: \",\n                                        userCustomRolesError\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 961,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 960,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_13__.TierGuard, {\n                                feature: \"custom_roles\",\n                                customMessage: \"Custom roles are available starting with the Starter plan. Create specialized roles to organize your API keys by task type and optimize routing for different use cases.\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowCreateCustomRoleForm(!showCreateCustomRoleForm),\n                                            className: \"btn-primary text-sm inline-flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 974,\n                                                    columnNumber: 19\n                                                }, this),\n                                                showCreateCustomRoleForm ? 'Cancel New Role' : 'Create New Custom Role'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 970,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 969,\n                                        columnNumber: 15\n                                    }, this),\n                                    showCreateCustomRoleForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-md font-medium text-gray-900 mb-3\",\n                                                children: \"Create New Custom Role for this Configuration\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 981,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"newCustomRoleId\",\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Role ID (short, no spaces, max 30 chars)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 984,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                id: \"newCustomRoleId\",\n                                                                value: newCustomRoleId,\n                                                                onChange: (e)=>setNewCustomRoleId(e.target.value.replace(/\\s/g, '')),\n                                                                className: \"form-input\",\n                                                                maxLength: 30,\n                                                                placeholder: \"e.g., my_blog_writer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 985,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 983,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"newCustomRoleName\",\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Display Name (max 100 chars)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 994,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                id: \"newCustomRoleName\",\n                                                                value: newCustomRoleName,\n                                                                onChange: (e)=>setNewCustomRoleName(e.target.value),\n                                                                className: \"form-input\",\n                                                                maxLength: 100,\n                                                                placeholder: \"e.g., My Awesome Blog Writer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 995,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 993,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"newCustomRoleDescription\",\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Description (optional, max 500 chars)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1004,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                id: \"newCustomRoleDescription\",\n                                                                value: newCustomRoleDescription,\n                                                                onChange: (e)=>setNewCustomRoleDescription(e.target.value),\n                                                                rows: 2,\n                                                                className: \"form-input\",\n                                                                maxLength: 500,\n                                                                placeholder: \"Optional: Describe what this role is for...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1005,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1003,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    createCustomRoleError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-red-50 border border-red-200 rounded-lg p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-800 text-sm\",\n                                                            children: createCustomRoleError\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1016,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1015,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleCreateCustomRole,\n                                                        disabled: isSavingCustomRole,\n                                                        className: \"btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                        children: isSavingCustomRole ? 'Saving Role...' : 'Save Custom Role'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1019,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 982,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 980,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 965,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 958,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-gray-700 mb-3\",\n                                children: \"Select roles to assign:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1033,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-y-auto space-y-2\",\n                                style: {\n                                    maxHeight: 'calc(90vh - 350px)'\n                                },\n                                children: [\n                                    isLoadingUserCustomRoles && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center py-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-orange-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1037,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-sm ml-2\",\n                                                children: \"Loading custom roles...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1038,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1036,\n                                        columnNumber: 17\n                                    }, this),\n                                    combinedRoles.map((role)=>{\n                                        const isAssigned = editingRolesApiKey.assigned_roles.some((ar)=>ar.id === role.id);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-3 rounded-lg border transition-all duration-200 \".concat(isAssigned ? 'bg-orange-50 border-orange-200 shadow-sm' : 'bg-white border-gray-200 hover:border-gray-300 hover:shadow-sm'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"role-\".concat(role.id),\n                                                    className: \"flex items-center cursor-pointer flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            id: \"role-\".concat(role.id),\n                                                            checked: isAssigned,\n                                                            onChange: ()=>handleRoleToggle(editingRolesApiKey, role.id, isAssigned),\n                                                            className: \"h-4 w-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500 focus:ring-2 cursor-pointer\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1050,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-3 text-sm font-medium \".concat(isAssigned ? 'text-orange-800' : 'text-gray-900'),\n                                                            children: role.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1057,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        role.isCustom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800\",\n                                                            children: \"Custom\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1061,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1049,\n                                                    columnNumber: 21\n                                                }, this),\n                                                role.isCustom && role.databaseId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDeleteCustomRole(role.databaseId, role.name),\n                                                    disabled: deletingCustomRoleId === role.databaseId,\n                                                    className: \"p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-wait ml-2\",\n                                                    title: \"Delete this custom role\",\n                                                    children: deletingCustomRoleId === role.databaseId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-4 w-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1073,\n                                                        columnNumber: 70\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1073,\n                                                        columnNumber: 123\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1067,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, role.id, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1044,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1034,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1032,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-t border-gray-200 bg-gray-50 rounded-b-xl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setEditingRolesApiKey(null);\n                                    setShowCreateCustomRoleForm(false);\n                                    setCreateCustomRoleError(null);\n                                },\n                                className: \"btn-secondary\",\n                                children: \"Done\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1084,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1083,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1082,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                lineNumber: 950,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n            lineNumber: 949,\n            columnNumber: 7\n        }, this);\n    };\n    // Main render logic with optimistic loading\n    if (showOptimisticLoading && !isCached(configId)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ManageKeysLoadingSkeleton__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n            lineNumber: 1099,\n            columnNumber: 12\n        }, this);\n    }\n    if (isLoadingConfig && !configDetails) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ManageKeysLoadingSkeleton__WEBPACK_IMPORTED_MODULE_9__.CompactManageKeysLoadingSkeleton, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n            lineNumber: 1103,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>navigateOptimistically('/my-models'),\n                        className: \"text-orange-600 hover:text-orange-700 inline-flex items-center mb-6 transition-colors duration-200 group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-5 w-5 mr-2 group-hover:-translate-x-1 transition-transform\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1114,\n                                columnNumber: 11\n                            }, this),\n                            \"Back to My API Models\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1110,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-2xl shadow-sm border border-gray-100 p-8 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: configDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-6 w-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1126,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1125,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                className: \"text-3xl font-bold text-gray-900\",\n                                                                children: configDetails.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1129,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500 mt-1\",\n                                                                children: \"Model Configuration\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1132,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1128,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1124,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-sm text-gray-600 bg-gray-50 px-4 py-2 rounded-xl w-fit\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-block w-2 h-2 bg-orange-500 rounded-full mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1136,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"ID: \",\n                                                    configDetails.id\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1135,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : error && !isLoadingConfig ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-red-100 rounded-2xl flex items-center justify-center mr-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-6 w-6 text-red-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1143,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1142,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold text-red-600\",\n                                                        children: \"Configuration Error\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1146,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-500 mt-1\",\n                                                        children: error.replace(\"Error loading model configuration: \", \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1147,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1145,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1141,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gray-100 rounded-2xl flex items-center justify-center mr-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-6 w-6 text-gray-400 animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1153,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1152,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: \"Loading Configuration...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1156,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 mt-1\",\n                                                        children: \"Please wait while we fetch your model details\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1157,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1155,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1151,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1121,\n                                    columnNumber: 13\n                                }, this),\n                                configDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>navigateOptimistically(\"/routing-setup/\".concat(configId, \"?from=model-config\")),\n                                        className: \"inline-flex items-center px-6 py-3 text-sm font-semibold rounded-2xl shadow-sm text-white bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 group\",\n                                        ...createRoutingHoverPrefetch(configId),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2 group-hover:rotate-90 transition-transform duration-200\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1174,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Advanced Routing Setup\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1169,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1165,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1120,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1119,\n                        columnNumber: 9\n                    }, this),\n                    successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-green-50 border border-green-200 rounded-2xl p-4 mb-6 animate-slide-in\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"h-5 w-5 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1186,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-green-800 font-medium\",\n                                    children: successMessage\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1187,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1185,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1184,\n                        columnNumber: 11\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-2xl p-4 mb-6 animate-slide-in\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-5 w-5 text-red-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1194,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-800 font-medium\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1195,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1193,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1192,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                lineNumber: 1109,\n                columnNumber: 7\n            }, this),\n            configDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-2xl shadow-sm border border-gray-100 p-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab('provider-keys'),\n                                    className: \"flex-1 px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 \".concat(activeTab === 'provider-keys' ? 'bg-orange-500 text-white shadow-md' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1215,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Provider API Keys\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1216,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1214,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1206,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab('user-api-keys'),\n                                    className: \"flex-1 px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 \".concat(activeTab === 'user-api-keys' ? 'bg-orange-500 text-white shadow-md' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1228,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Generated API Keys\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1229,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1227,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1219,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1205,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1204,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'provider-keys' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 xl:grid-cols-5 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"xl:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-2xl shadow-sm border border-gray-100 p-6 sticky top-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mr-3 shadow-md\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"h-5 w-5 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1243,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1242,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-xl font-bold text-gray-900\",\n                                                            children: \"Add Provider API Key\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1246,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: \"Configure new provider key\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1247,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1245,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1241,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSaveKey,\n                                            className: \"space-y-5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"provider\",\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: \"Provider\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1254,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    id: \"provider\",\n                                                                    value: provider,\n                                                                    onChange: (e)=>{\n                                                                        setProvider(e.target.value);\n                                                                    },\n                                                                    className: \"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm\",\n                                                                    children: PROVIDER_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: option.value,\n                                                                            children: option.label\n                                                                        }, option.value, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1264,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1257,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1253,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"apiKeyRaw\",\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: \"API Key\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1270,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"apiKeyRaw\",\n                                                                    type: \"password\",\n                                                                    value: apiKeyRaw,\n                                                                    onChange: (e)=>setApiKeyRaw(e.target.value),\n                                                                    className: \"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm\",\n                                                                    placeholder: \"Enter your API key\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1273,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                isFetchingProviderModels && fetchedProviderModels === null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"mt-2 text-xs text-orange-600 flex items-center bg-orange-50 p-2 rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-1 animate-pulse\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1284,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Fetching models...\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1283,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                fetchProviderModelsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"mt-2 text-xs text-red-600 bg-red-50 p-2 rounded-lg\",\n                                                                    children: fetchProviderModelsError\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1289,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1269,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"predefinedModelId\",\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: \"Model Variant\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1294,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    id: \"predefinedModelId\",\n                                                                    value: predefinedModelId,\n                                                                    onChange: (e)=>setPredefinedModelId(e.target.value),\n                                                                    disabled: !modelOptions.length,\n                                                                    className: \"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm disabled:bg-gray-50 disabled:text-gray-500\",\n                                                                    children: modelOptions.length > 0 ? modelOptions.map((m)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: m.value,\n                                                                            children: m.label\n                                                                        }, m.value, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1306,\n                                                                            columnNumber: 27\n                                                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        disabled: true,\n                                                                        children: fetchedProviderModels === null && isFetchingProviderModels ? \"Loading models...\" : \"Select a provider first\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1309,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1297,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1293,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"label\",\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: \"Label\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1315,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    id: \"label\",\n                                                                    value: label,\n                                                                    onChange: (e)=>setLabel(e.target.value),\n                                                                    required: true,\n                                                                    className: \"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm\",\n                                                                    placeholder: \"e.g., My OpenAI GPT-4o Key #1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1318,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1314,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"temperature\",\n                                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                    children: [\n                                                                        \"Temperature\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500 ml-1\",\n                                                                            children: \"(0.0 - 2.0)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1332,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1330,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"range\",\n                                                                            id: \"temperature\",\n                                                                            min: \"0\",\n                                                                            max: \"2\",\n                                                                            step: \"0.1\",\n                                                                            value: temperature,\n                                                                            onChange: (e)=>setTemperature(parseFloat(e.target.value)),\n                                                                            className: \"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider-orange\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1335,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex justify-between items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-gray-500\",\n                                                                                    children: \"Conservative\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1346,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"number\",\n                                                                                        min: \"0\",\n                                                                                        max: \"2\",\n                                                                                        step: \"0.1\",\n                                                                                        value: temperature,\n                                                                                        onChange: (e)=>setTemperature(Math.min(2.0, Math.max(0.0, parseFloat(e.target.value) || 0))),\n                                                                                        className: \"w-16 px-2 py-1 text-xs border border-gray-200 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                        lineNumber: 1348,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1347,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-gray-500\",\n                                                                                    children: \"Creative\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1358,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1345,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: \"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1360,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1334,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1329,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1252,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    disabled: isSavingKey || !predefinedModelId || predefinedModelId === '' || !apiKeyRaw.trim() || !label.trim(),\n                                                    className: \"w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none text-sm\",\n                                                    children: isSavingKey ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"flex items-center justify-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2 animate-pulse\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1374,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Saving...\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1373,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"flex items-center justify-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1379,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Add API Key\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1378,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1367,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1251,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        className: \"h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1389,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-blue-900 mb-1\",\n                                                                children: \"Key Configuration Rules\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1391,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-blue-800 space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: [\n                                                                            \"✅ \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"Same API key, different models:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1393,\n                                                                                columnNumber: 28\n                                                                            }, this),\n                                                                            \" Allowed\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1393,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: [\n                                                                            \"✅ \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"Different API keys, same model:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1394,\n                                                                                columnNumber: 28\n                                                                            }, this),\n                                                                            \" Allowed\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1394,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: [\n                                                                            \"❌ \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"Same model twice:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1395,\n                                                                                columnNumber: 28\n                                                                            }, this),\n                                                                            \" Not allowed in one configuration\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1395,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1392,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1390,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1388,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1387,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1240,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1239,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"xl:col-span-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-2xl shadow-sm border border-gray-100 p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-3 shadow-md\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-5 w-5 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1408,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1407,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-xl font-bold text-gray-900\",\n                                                            children: \"API Keys & Roles\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1411,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: \"Manage existing keys\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1412,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1410,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1406,\n                                            columnNumber: 15\n                                        }, this),\n                                        isLoadingKeysAndRoles && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-8 w-8 text-gray-400 mx-auto mb-3 animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1418,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-sm\",\n                                                    children: \"Loading API keys...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1419,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1417,\n                                            columnNumber: 17\n                                        }, this),\n                                        !isLoadingKeysAndRoles && savedKeysWithRoles.length === 0 && (!error || error && error.startsWith(\"Error loading model configuration:\")) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-6 w-6 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1426,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1425,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-semibold text-gray-900 mb-1\",\n                                                    children: \"No API Keys\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1428,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: \"Add your first key using the form\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1429,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1424,\n                                            columnNumber: 17\n                                        }, this),\n                                        !isLoadingKeysAndRoles && savedKeysWithRoles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3 max-h-96 overflow-y-auto\",\n                                            children: savedKeysWithRoles.map((key, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-50 border border-gray-200 rounded-xl p-4 hover:shadow-md transition-all duration-200 animate-slide-in\",\n                                                    style: {\n                                                        animationDelay: \"\".concat(index * 50, \"ms\")\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1 min-w-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center mb-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"text-sm font-semibold text-gray-900 truncate mr-2\",\n                                                                                children: key.label\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1440,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            key.is_default_general_chat_model && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 flex-shrink-0\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                        className: \"h-3 w-3 mr-1\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                        lineNumber: 1443,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    \"Default\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1442,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1439,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-gray-900 bg-white px-2 py-1 rounded-lg border\",\n                                                                                        children: [\n                                                                                            key.provider,\n                                                                                            \" (\",\n                                                                                            key.predefined_model_id,\n                                                                                            \")\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                        lineNumber: 1451,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded-lg border border-orange-200\",\n                                                                                        children: [\n                                                                                            \"Temp: \",\n                                                                                            key.temperature\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                        lineNumber: 1454,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1450,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex flex-wrap gap-1\",\n                                                                                children: key.assigned_roles.length > 0 ? key.assigned_roles.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"inline-block whitespace-nowrap rounded-full bg-orange-100 px-2 py-1 text-xs font-medium text-orange-800\",\n                                                                                        children: role.name\n                                                                                    }, role.id, false, {\n                                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                        lineNumber: 1462,\n                                                                                        columnNumber: 35\n                                                                                    }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-gray-500 bg-white px-2 py-1 rounded-lg border\",\n                                                                                    children: \"No roles\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                    lineNumber: 1467,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1459,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1449,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    !key.is_default_general_chat_model && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleSetDefaultChatKey(key.id),\n                                                                        className: \"text-xs bg-white border border-gray-200 hover:border-gray-300 text-gray-700 hover:text-gray-900 py-1 px-2 rounded-lg mt-2 transition-colors\",\n                                                                        \"data-tooltip-id\": \"global-tooltip\",\n                                                                        \"data-tooltip-content\": \"Set as default chat model\",\n                                                                        children: \"Set Default\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1473,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1438,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1 ml-2 flex-shrink-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleEditKey(key),\n                                                                        disabled: isDeletingKey === key.id,\n                                                                        className: \"p-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors disabled:opacity-50\",\n                                                                        \"data-tooltip-id\": \"global-tooltip\",\n                                                                        \"data-tooltip-content\": \"Edit Model & Settings\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1492,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1485,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_13__.TierGuard, {\n                                                                        feature: \"custom_roles\",\n                                                                        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            disabled: true,\n                                                                            className: \"p-2 text-gray-400 cursor-not-allowed rounded-lg opacity-50\",\n                                                                            \"data-tooltip-id\": \"global-tooltip\",\n                                                                            \"data-tooltip-content\": \"Role management requires Starter plan or higher\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1503,\n                                                                                columnNumber: 33\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1497,\n                                                                            columnNumber: 31\n                                                                        }, void 0),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>setEditingRolesApiKey(key),\n                                                                            disabled: isDeletingKey === key.id,\n                                                                            className: \"p-2 text-orange-600 hover:text-orange-700 hover:bg-orange-50 rounded-lg transition-colors disabled:opacity-50\",\n                                                                            \"data-tooltip-id\": \"global-tooltip\",\n                                                                            \"data-tooltip-content\": \"Manage Roles\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1514,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1507,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1494,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleDeleteKey(key.id, key.label),\n                                                                        disabled: isDeletingKey === key.id,\n                                                                        className: \"p-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50\",\n                                                                        \"data-tooltip-id\": \"global-tooltip\",\n                                                                        \"data-tooltip-content\": \"Delete Key\",\n                                                                        children: isDeletingKey === key.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"h-4 w-4 animate-pulse\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1525,\n                                                                            columnNumber: 31\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                            lineNumber: 1527,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1517,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1484,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1437,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, key.id, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1436,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1434,\n                                            columnNumber: 17\n                                        }, this),\n                                        !isLoadingKeysAndRoles && error && !error.startsWith(\"Error loading model configuration:\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-red-50 border border-red-200 rounded-xl p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-5 w-5 text-red-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1540,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-800 font-medium text-sm\",\n                                                        children: [\n                                                            \"Could not load API keys/roles: \",\n                                                            error\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1541,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1539,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1538,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1405,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1404,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1237,\n                        columnNumber: 13\n                    }, this),\n                    activeTab === 'user-api-keys' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-2xl shadow-sm border border-gray-100 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UserApiKeys_ApiKeyManager__WEBPACK_IMPORTED_MODULE_12__.ApiKeyManager, {\n                            configId: configId,\n                            configName: configDetails.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1553,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1552,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                lineNumber: 1202,\n                columnNumber: 9\n            }, this),\n            editingRolesApiKey && renderManageRolesModal(),\n            editingApiKey && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card w-full max-w-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center p-6 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"Edit API Key\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1570,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setEditingApiKey(null),\n                                    className: \"text-gray-500 hover:text-gray-700 hover:bg-gray-100 p-1 rounded-lg transition-colors duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1575,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1571,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1569,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: editingApiKey.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1581,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                \"Current: \",\n                                                editingApiKey.provider,\n                                                \" (\",\n                                                editingApiKey.predefined_model_id,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1582,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1580,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Provider\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1589,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full p-2.5 bg-gray-50 border border-gray-300 rounded-md text-gray-700\",\n                                                    children: ((_llmProviders_find = _config_models__WEBPACK_IMPORTED_MODULE_3__.llmProviders.find((p)=>p.id === editingApiKey.provider)) === null || _llmProviders_find === void 0 ? void 0 : _llmProviders_find.name) || editingApiKey.provider\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1592,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 mt-1\",\n                                                    children: \"Provider cannot be changed\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1595,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1588,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"editModelId\",\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Model\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1599,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    id: \"editModelId\",\n                                                    value: editPredefinedModelId,\n                                                    onChange: (e)=>setEditPredefinedModelId(e.target.value),\n                                                    disabled: !editModelOptions.length,\n                                                    className: \"w-full p-2.5 bg-white border border-gray-300 rounded-md text-gray-900 focus:ring-orange-500 focus:border-orange-500 disabled:opacity-50 disabled:bg-gray-100\",\n                                                    children: editModelOptions.length > 0 ? editModelOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: option.value,\n                                                            children: option.label\n                                                        }, option.value, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1611,\n                                                            columnNumber: 25\n                                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        disabled: true,\n                                                        children: isFetchingProviderModels ? 'Loading models...' : 'No models available'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1616,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1602,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1598,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"editTemperature\",\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: [\n                                                        \"Temperature: \",\n                                                        editTemperature\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1624,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"range\",\n                                                    id: \"editTemperature\",\n                                                    min: \"0\",\n                                                    max: \"2\",\n                                                    step: \"0.1\",\n                                                    value: editTemperature,\n                                                    onChange: (e)=>setEditTemperature(parseFloat(e.target.value)),\n                                                    className: \"slider-orange w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1627,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-xs text-gray-500 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"0.0 (Focused)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1638,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"1.0 (Balanced)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1639,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"2.0 (Creative)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 1640,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1637,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1623,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 rounded-lg p-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-600\",\n                                                children: \"You can change the model and temperature settings. Temperature controls randomness in responses. Lower values (0.0-0.3) are more focused and deterministic, while higher values (1.5-2.0) are more creative and varied.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1645,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1644,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1587,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1579,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 border-t border-gray-200 bg-gray-50 rounded-b-xl\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setEditingApiKey(null),\n                                        className: \"btn-secondary\",\n                                        disabled: isSavingEdit,\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1654,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSaveEdit,\n                                        disabled: isSavingEdit,\n                                        className: \"btn-primary\",\n                                        children: isSavingEdit ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1668,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Saving...\"\n                                            ]\n                                        }, void 0, true) : 'Save Changes'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1661,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1653,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1652,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1568,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                lineNumber: 1567,\n                columnNumber: 9\n            }, this),\n            !configDetails && !isLoadingConfig && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-2xl shadow-sm border border-gray-100 text-center py-16 px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                            className: \"h-8 w-8 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1684,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1683,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-gray-900 mb-3\",\n                        children: \"Model Not Found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1686,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600 mb-8\",\n                        children: \"This API Model configuration could not be found or may have been deleted.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1687,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>navigateOptimistically('/my-models'),\n                        className: \"inline-flex items-center px-6 py-3 text-sm font-medium rounded-xl text-white bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckCircleIcon_CloudArrowDownIcon_Cog6ToothIcon_GlobeAltIcon_InformationCircleIcon_KeyIcon_PencilIcon_PlusCircleIcon_PlusIcon_ShieldCheckIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1692,\n                                columnNumber: 13\n                            }, this),\n                            \"Return to My API Models\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1688,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                lineNumber: 1682,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: confirmation.isOpen,\n                onClose: confirmation.hideConfirmation,\n                onConfirm: confirmation.onConfirm,\n                title: confirmation.title,\n                message: confirmation.message,\n                confirmText: confirmation.confirmText,\n                cancelText: confirmation.cancelText,\n                type: confirmation.type,\n                isLoading: confirmation.isLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                lineNumber: 1699,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tooltip__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                id: \"global-tooltip\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n                lineNumber: 1711,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\",\n        lineNumber: 1107,\n        columnNumber: 5\n    }, this);\n}\n_s(ConfigDetailsPage, \"myF+3Ly6oNmTGDOomavYHp3wooQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_7__.useConfirmation,\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_11__.useNavigationSafe,\n        _hooks_useManageKeysPrefetch__WEBPACK_IMPORTED_MODULE_8__.useManageKeysPrefetch,\n        _hooks_useRoutingSetupPrefetch__WEBPACK_IMPORTED_MODULE_10__.useRoutingSetupPrefetch\n    ];\n});\n_c2 = ConfigDetailsPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"PROVIDER_OPTIONS$llmProviders.map\");\n$RefreshReg$(_c1, \"PROVIDER_OPTIONS\");\n$RefreshReg$(_c2, \"ConfigDetailsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/my-models/[configId]/page.tsx\n"));

/***/ })

});