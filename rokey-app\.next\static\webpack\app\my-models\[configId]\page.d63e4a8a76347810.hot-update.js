"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/[configId]/page",{

/***/ "(app-pages-browser)/./src/components/UserApiKeys/ApiKeyManager.tsx":
/*!******************************************************!*\
  !*** ./src/components/UserApiKeys/ApiKeyManager.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiKeyManager: () => (/* binding */ ApiKeyManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _ApiKeyCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ApiKeyCard */ \"(app-pages-browser)/./src/components/UserApiKeys/ApiKeyCard.tsx\");\n/* harmony import */ var _CreateApiKeyDialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CreateApiKeyDialog */ \"(app-pages-browser)/./src/components/UserApiKeys/CreateApiKeyDialog.tsx\");\n/* harmony import */ var _EditApiKeyDialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./EditApiKeyDialog */ \"(app-pages-browser)/./src/components/UserApiKeys/EditApiKeyDialog.tsx\");\n/* harmony import */ var _ApiKeyUsageDialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ApiKeyUsageDialog */ \"(app-pages-browser)/./src/components/UserApiKeys/ApiKeyUsageDialog.tsx\");\n/* harmony import */ var _components_TierEnforcement__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/TierEnforcement */ \"(app-pages-browser)/./src/components/TierEnforcement/index.ts\");\n/* harmony import */ var _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/useConfirmation */ \"(app-pages-browser)/./src/hooks/useConfirmation.ts\");\n/* __next_internal_client_entry_do_not_use__ ApiKeyManager auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction ApiKeyManager(param) {\n    let { configId, configName } = param;\n    _s();\n    const [apiKeys, setApiKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [creating, setCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCreateDialog, setShowCreateDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [subscriptionInfo, setSubscriptionInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const confirmation = (0,_hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_10__.useConfirmation)();\n    // Fetch API keys for this configuration\n    const fetchApiKeys = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/user-api-keys?config_id=\".concat(configId));\n            if (!response.ok) {\n                throw new Error('Failed to fetch API keys');\n            }\n            const data = await response.json();\n            setApiKeys(data.api_keys || []);\n        } catch (error) {\n            console.error('Error fetching API keys:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error('Failed to load API keys');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Fetch subscription info\n    const fetchSubscriptionInfo = async (currentKeyCount)=>{\n        try {\n            // Get the tier from the user's active subscription\n            const tierResponse = await fetch('/api/user/subscription-tier');\n            const tierData = tierResponse.ok ? await tierResponse.json() : null;\n            const tier = (tierData === null || tierData === void 0 ? void 0 : tierData.tier) || 'starter';\n            const limits = {\n                free: 3,\n                starter: 50,\n                professional: 999999,\n                enterprise: 999999\n            };\n            // Use provided count or current apiKeys length\n            const keyCount = currentKeyCount !== undefined ? currentKeyCount : apiKeys.length;\n            setSubscriptionInfo({\n                tier,\n                keyLimit: limits[tier] || limits.free,\n                currentCount: keyCount\n            });\n        } catch (error) {\n            console.error('Error fetching subscription info:', error);\n            // Fallback to free tier\n            const keyCount = currentKeyCount !== undefined ? currentKeyCount : apiKeys.length;\n            setSubscriptionInfo({\n                tier: 'free',\n                keyLimit: 3,\n                currentCount: keyCount\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ApiKeyManager.useEffect\": ()=>{\n            fetchApiKeys();\n        }\n    }[\"ApiKeyManager.useEffect\"], [\n        configId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ApiKeyManager.useEffect\": ()=>{\n            if (apiKeys.length >= 0) {\n                fetchSubscriptionInfo();\n            }\n        }\n    }[\"ApiKeyManager.useEffect\"], [\n        apiKeys\n    ]);\n    const handleCreateApiKey = async (keyData)=>{\n        try {\n            setCreating(true);\n            const response = await fetch('/api/user-api-keys', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...keyData,\n                    custom_api_config_id: configId\n                })\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                throw new Error(error.error || 'Failed to create API key');\n            }\n            const newApiKey = await response.json();\n            // Show the full API key in a special dialog since it's only shown once\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success('API key created successfully!');\n            // Optimistically add the new key to the list immediately\n            const optimisticKey = {\n                ...newApiKey,\n                // Add any missing fields that might be needed for display\n                custom_api_configs: {\n                    id: configId,\n                    name: configName\n                }\n            };\n            setApiKeys((prevKeys)=>{\n                const newKeys = [\n                    optimisticKey,\n                    ...prevKeys\n                ];\n                // Immediately update subscription info with new count\n                fetchSubscriptionInfo(newKeys.length);\n                return newKeys;\n            });\n            // Also refresh to ensure we have the latest data and correct counts\n            await fetchApiKeys();\n            return newApiKey;\n        } catch (error) {\n            console.error('Error creating API key:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(error.message || 'Failed to create API key');\n            throw error;\n        } finally{\n            setCreating(false);\n        }\n    };\n    const handleEditApiKey = async (keyId, updates)=>{\n        try {\n            // Optimistically update the key in the list\n            const previousKeys = [\n                ...apiKeys\n            ];\n            setApiKeys((prevKeys)=>prevKeys.map((key)=>key.id === keyId ? {\n                        ...key,\n                        ...updates\n                    } : key));\n            const response = await fetch(\"/api/user-api-keys/\".concat(keyId), {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(updates)\n            });\n            if (!response.ok) {\n                // Revert the optimistic update on error\n                setApiKeys(previousKeys);\n                const error = await response.json();\n                throw new Error(error.error || 'Failed to update API key');\n            }\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success('API key updated successfully');\n            // Refresh to ensure we have the latest data\n            await fetchApiKeys();\n            setShowEditDialog(false);\n            setSelectedApiKey(null);\n        } catch (error) {\n            console.error('Error updating API key:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(error.message || 'Failed to update API key');\n        }\n    };\n    const handleRevokeApiKey = async (keyId)=>{\n        const apiKey = apiKeys.find((key)=>key.id === keyId);\n        const keyName = (apiKey === null || apiKey === void 0 ? void 0 : apiKey.key_name) || 'this API key';\n        confirmation.showConfirmation({\n            title: 'Revoke API Key',\n            message: 'Are you sure you want to revoke \"'.concat(keyName, '\"? This action cannot be undone and will immediately disable the key.'),\n            confirmText: 'Revoke Key',\n            cancelText: 'Cancel',\n            type: 'danger'\n        }, async ()=>{\n            try {\n                // Optimistically update the key status to revoked instead of removing it\n                const previousKeys = [\n                    ...apiKeys\n                ];\n                setApiKeys((prevKeys)=>prevKeys.map((key)=>key.id === keyId ? {\n                            ...key,\n                            status: 'revoked'\n                        } : key));\n                const response = await fetch(\"/api/user-api-keys/\".concat(keyId), {\n                    method: 'DELETE'\n                });\n                if (!response.ok) {\n                    // Revert the optimistic update on error\n                    setApiKeys(previousKeys);\n                    const error = await response.json();\n                    throw new Error(error.error || 'Failed to revoke API key');\n                }\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success('API key revoked successfully');\n                // Refresh to ensure we have the latest data\n                await fetchApiKeys();\n            } catch (error) {\n                console.error('Error revoking API key:', error);\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(error.message || 'Failed to revoke API key');\n                throw error; // Re-throw to let the confirmation modal handle the error state\n            }\n        });\n    };\n    const handleViewUsage = (keyId)=>{\n        const apiKey = apiKeys.find((key)=>key.id === keyId);\n        setSelectedApiKey(apiKey);\n        setShowUsageDialog(true);\n    };\n    const handleCreateDialogClose = (open)=>{\n        setShowCreateDialog(open);\n    // Note: We no longer need to refresh here since we refresh immediately after creation\n    };\n    const canCreateMoreKeys = subscriptionInfo ? subscriptionInfo.currentCount < subscriptionInfo.keyLimit : true;\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"flex items-center justify-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"h-6 w-6 animate-spin mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 11\n                    }, this),\n                    \"Loading API keys...\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 253,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n            lineNumber: 252,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"API Keys\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: [\n                                    \"Generate API keys for programmatic access to \",\n                                    configName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this),\n                    canCreateMoreKeys ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: ()=>setShowCreateDialog(true),\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 13\n                            }, this),\n                            \"Create API Key\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-end gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                disabled: true,\n                                className: \"flex items-center gap-2 opacity-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Create API Key\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-orange-600 font-medium\",\n                                children: (subscriptionInfo === null || subscriptionInfo === void 0 ? void 0 : subscriptionInfo.tier) === 'free' ? 'Upgrade to Starter plan for more API keys' : 'API key limit reached - upgrade for unlimited keys'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 264,\n                columnNumber: 7\n            }, this),\n            subscriptionInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg border border-gray-200 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_9__.LimitIndicator, {\n                    current: subscriptionInfo.currentCount,\n                    limit: subscriptionInfo.keyLimit,\n                    label: \"User-Generated API Keys\",\n                    tier: subscriptionInfo.tier,\n                    showUpgradeHint: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 303,\n                columnNumber: 9\n            }, this),\n            apiKeys.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"text-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"h-12 w-12 mx-auto text-gray-400 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-2\",\n                            children: \"No API Keys\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Create your first API key to start using the RouKey API programmatically.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 13\n                        }, this),\n                        canCreateMoreKeys ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: ()=>setShowCreateDialog(true),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 17\n                                }, this),\n                                \"Create Your First API Key\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    disabled: true,\n                                    className: \"opacity-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Create Your First API Key\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-orange-600 font-medium\",\n                                    children: (subscriptionInfo === null || subscriptionInfo === void 0 ? void 0 : subscriptionInfo.tier) === 'free' ? 'Upgrade to Starter plan to create API keys' : 'API key limit reached - upgrade for unlimited keys'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 316,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4\",\n                children: apiKeys.map((apiKey)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ApiKeyCard__WEBPACK_IMPORTED_MODULE_5__.ApiKeyCard, {\n                        apiKey: apiKey,\n                        onEdit: (key)=>{\n                            setSelectedApiKey(key);\n                            setShowEditDialog(true);\n                        },\n                        onRevoke: handleRevokeApiKey,\n                        onViewUsage: handleViewUsage\n                    }, apiKey.id, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 349,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 347,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateApiKeyDialog__WEBPACK_IMPORTED_MODULE_6__.CreateApiKeyDialog, {\n                open: showCreateDialog,\n                onOpenChange: handleCreateDialogClose,\n                onCreateApiKey: handleCreateApiKey,\n                configName: configName,\n                creating: creating,\n                subscriptionTier: (subscriptionInfo === null || subscriptionInfo === void 0 ? void 0 : subscriptionInfo.tier) || 'starter'\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 364,\n                columnNumber: 7\n            }, this),\n            selectedApiKey && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EditApiKeyDialog__WEBPACK_IMPORTED_MODULE_7__.EditApiKeyDialog, {\n                        open: showEditDialog,\n                        onOpenChange: setShowEditDialog,\n                        apiKey: selectedApiKey,\n                        onUpdateApiKey: (updates)=>handleEditApiKey(selectedApiKey.id, updates)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ApiKeyUsageDialog__WEBPACK_IMPORTED_MODULE_8__.ApiKeyUsageDialog, {\n                        open: showUsageDialog,\n                        onOpenChange: setShowUsageDialog,\n                        apiKey: selectedApiKey\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n        lineNumber: 262,\n        columnNumber: 5\n    }, this);\n}\n_s(ApiKeyManager, \"wzx+xNoPucKu2oL+bFaGPsR3hPI=\", false, function() {\n    return [\n        _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_10__.useConfirmation\n    ];\n});\n_c = ApiKeyManager;\nvar _c;\n$RefreshReg$(_c, \"ApiKeyManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1VzZXJBcGlLZXlzL0FwaUtleU1hbmFnZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVtRDtBQUNIO0FBQ2dDO0FBRWY7QUFDbEM7QUFDVztBQUNnQjtBQUNKO0FBQ0U7QUFFTTtBQUVKO0FBT25ELFNBQVNnQixjQUFjLEtBQTRDO1FBQTVDLEVBQUVDLFFBQVEsRUFBRUMsVUFBVSxFQUFzQixHQUE1Qzs7SUFDNUIsTUFBTSxDQUFDQyxTQUFTQyxXQUFXLEdBQUduQiwrQ0FBUUEsQ0FBUSxFQUFFO0lBQ2hELE1BQU0sQ0FBQ29CLFNBQVNDLFdBQVcsR0FBR3JCLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ3NCLFVBQVVDLFlBQVksR0FBR3ZCLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQ3dCLGtCQUFrQkMsb0JBQW9CLEdBQUd6QiwrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUMwQixrQkFBa0JDLG9CQUFvQixHQUFHM0IsK0NBQVFBLENBSTlDO0lBRVYsTUFBTTRCLGVBQWVkLHdFQUFlQTtJQUVwQyx3Q0FBd0M7SUFDeEMsTUFBTWUsZUFBZTtRQUNuQixJQUFJO1lBQ0ZSLFdBQVc7WUFDWCxNQUFNUyxXQUFXLE1BQU1DLE1BQU0sZ0NBQXlDLE9BQVRmO1lBRTdELElBQUksQ0FBQ2MsU0FBU0UsRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUlDLE1BQU07WUFDbEI7WUFFQSxNQUFNQyxPQUFPLE1BQU1KLFNBQVNLLElBQUk7WUFDaENoQixXQUFXZSxLQUFLRSxRQUFRLElBQUksRUFBRTtRQUNoQyxFQUFFLE9BQU9DLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDRCQUE0QkE7WUFDMUM3Qix5Q0FBS0EsQ0FBQzZCLEtBQUssQ0FBQztRQUNkLFNBQVU7WUFDUmhCLFdBQVc7UUFDYjtJQUNGO0lBRUEsMEJBQTBCO0lBQzFCLE1BQU1rQix3QkFBd0IsT0FBT0M7UUFDbkMsSUFBSTtZQUNGLG1EQUFtRDtZQUNuRCxNQUFNQyxlQUFlLE1BQU1WLE1BQU07WUFDakMsTUFBTVcsV0FBV0QsYUFBYVQsRUFBRSxHQUFHLE1BQU1TLGFBQWFOLElBQUksS0FBSztZQUMvRCxNQUFNUSxPQUFPRCxDQUFBQSxxQkFBQUEsK0JBQUFBLFNBQVVDLElBQUksS0FBSTtZQUUvQixNQUFNQyxTQUFTO2dCQUNiQyxNQUFNO2dCQUNOQyxTQUFTO2dCQUNUQyxjQUFjO2dCQUNkQyxZQUFZO1lBQ2Q7WUFFQSwrQ0FBK0M7WUFDL0MsTUFBTUMsV0FBV1Qsb0JBQW9CVSxZQUFZVixrQkFBa0J0QixRQUFRaUMsTUFBTTtZQUVqRnhCLG9CQUFvQjtnQkFDbEJnQjtnQkFDQVMsVUFBVVIsTUFBTSxDQUFDRCxLQUE0QixJQUFJQyxPQUFPQyxJQUFJO2dCQUM1RFEsY0FBY0o7WUFDaEI7UUFDRixFQUFFLE9BQU9aLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLHFDQUFxQ0E7WUFDbkQsd0JBQXdCO1lBQ3hCLE1BQU1ZLFdBQVdULG9CQUFvQlUsWUFBWVYsa0JBQWtCdEIsUUFBUWlDLE1BQU07WUFDakZ4QixvQkFBb0I7Z0JBQ2xCZ0IsTUFBTTtnQkFDTlMsVUFBVTtnQkFDVkMsY0FBY0o7WUFDaEI7UUFDRjtJQUNGO0lBRUFoRCxnREFBU0E7bUNBQUM7WUFDUjRCO1FBQ0Y7a0NBQUc7UUFBQ2I7S0FBUztJQUViZixnREFBU0E7bUNBQUM7WUFDUixJQUFJaUIsUUFBUWlDLE1BQU0sSUFBSSxHQUFHO2dCQUN2Qlo7WUFDRjtRQUNGO2tDQUFHO1FBQUNyQjtLQUFRO0lBRVosTUFBTW9DLHFCQUFxQixPQUFPQztRQUNoQyxJQUFJO1lBQ0ZoQyxZQUFZO1lBQ1osTUFBTU8sV0FBVyxNQUFNQyxNQUFNLHNCQUFzQjtnQkFDakR5QixRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFDbkIsR0FBR0wsT0FBTztvQkFDVk0sc0JBQXNCN0M7Z0JBQ3hCO1lBQ0Y7WUFFQSxJQUFJLENBQUNjLFNBQVNFLEVBQUUsRUFBRTtnQkFDaEIsTUFBTUssUUFBUSxNQUFNUCxTQUFTSyxJQUFJO2dCQUNqQyxNQUFNLElBQUlGLE1BQU1JLE1BQU1BLEtBQUssSUFBSTtZQUNqQztZQUVBLE1BQU15QixZQUFZLE1BQU1oQyxTQUFTSyxJQUFJO1lBRXJDLHVFQUF1RTtZQUN2RTNCLHlDQUFLQSxDQUFDdUQsT0FBTyxDQUFDO1lBRWQseURBQXlEO1lBQ3pELE1BQU1DLGdCQUFnQjtnQkFDcEIsR0FBR0YsU0FBUztnQkFDWiwwREFBMEQ7Z0JBQzFERyxvQkFBb0I7b0JBQ2xCQyxJQUFJbEQ7b0JBQ0ptRCxNQUFNbEQ7Z0JBQ1I7WUFDRjtZQUNBRSxXQUFXaUQsQ0FBQUE7Z0JBQ1QsTUFBTUMsVUFBVTtvQkFBQ0w7dUJBQWtCSTtpQkFBUztnQkFDNUMsc0RBQXNEO2dCQUN0RDdCLHNCQUFzQjhCLFFBQVFsQixNQUFNO2dCQUNwQyxPQUFPa0I7WUFDVDtZQUVBLG9FQUFvRTtZQUNwRSxNQUFNeEM7WUFFTixPQUFPaUM7UUFDVCxFQUFFLE9BQU96QixPQUFZO1lBQ25CQyxRQUFRRCxLQUFLLENBQUMsMkJBQTJCQTtZQUN6QzdCLHlDQUFLQSxDQUFDNkIsS0FBSyxDQUFDQSxNQUFNaUMsT0FBTyxJQUFJO1lBQzdCLE1BQU1qQztRQUNSLFNBQVU7WUFDUmQsWUFBWTtRQUNkO0lBQ0Y7SUFFQSxNQUFNZ0QsbUJBQW1CLE9BQU9DLE9BQWVDO1FBQzdDLElBQUk7WUFDRiw0Q0FBNEM7WUFDNUMsTUFBTUMsZUFBZTttQkFBSXhEO2FBQVE7WUFDakNDLFdBQVdpRCxDQUFBQSxXQUNUQSxTQUFTTyxHQUFHLENBQUNDLENBQUFBLE1BQ1hBLElBQUlWLEVBQUUsS0FBS00sUUFBUTt3QkFBRSxHQUFHSSxHQUFHO3dCQUFFLEdBQUdILE9BQU87b0JBQUMsSUFBSUc7WUFJaEQsTUFBTTlDLFdBQVcsTUFBTUMsTUFBTSxzQkFBNEIsT0FBTnlDLFFBQVM7Z0JBQzFEaEIsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUNhO1lBQ3ZCO1lBRUEsSUFBSSxDQUFDM0MsU0FBU0UsRUFBRSxFQUFFO2dCQUNoQix3Q0FBd0M7Z0JBQ3hDYixXQUFXdUQ7Z0JBQ1gsTUFBTXJDLFFBQVEsTUFBTVAsU0FBU0ssSUFBSTtnQkFDakMsTUFBTSxJQUFJRixNQUFNSSxNQUFNQSxLQUFLLElBQUk7WUFDakM7WUFFQTdCLHlDQUFLQSxDQUFDdUQsT0FBTyxDQUFDO1lBQ2QsNENBQTRDO1lBQzVDLE1BQU1sQztZQUNOZ0Qsa0JBQWtCO1lBQ2xCQyxrQkFBa0I7UUFDcEIsRUFBRSxPQUFPekMsT0FBWTtZQUNuQkMsUUFBUUQsS0FBSyxDQUFDLDJCQUEyQkE7WUFDekM3Qix5Q0FBS0EsQ0FBQzZCLEtBQUssQ0FBQ0EsTUFBTWlDLE9BQU8sSUFBSTtRQUMvQjtJQUNGO0lBRUEsTUFBTVMscUJBQXFCLE9BQU9QO1FBQ2hDLE1BQU1RLFNBQVM5RCxRQUFRK0QsSUFBSSxDQUFDTCxDQUFBQSxNQUFPQSxJQUFJVixFQUFFLEtBQUtNO1FBQzlDLE1BQU1VLFVBQVVGLENBQUFBLG1CQUFBQSw2QkFBQUEsT0FBUUcsUUFBUSxLQUFJO1FBRXBDdkQsYUFBYXdELGdCQUFnQixDQUMzQjtZQUNFQyxPQUFPO1lBQ1BmLFNBQVMsb0NBQTRDLE9BQVJZLFNBQVE7WUFDckRJLGFBQWE7WUFDYkMsWUFBWTtZQUNaQyxNQUFNO1FBQ1IsR0FDQTtZQUNFLElBQUk7Z0JBQ0YseUVBQXlFO2dCQUN6RSxNQUFNZCxlQUFlO3VCQUFJeEQ7aUJBQVE7Z0JBQ2pDQyxXQUFXaUQsQ0FBQUEsV0FDVEEsU0FBU08sR0FBRyxDQUFDQyxDQUFBQSxNQUNYQSxJQUFJVixFQUFFLEtBQUtNLFFBQVE7NEJBQUUsR0FBR0ksR0FBRzs0QkFBRWEsUUFBUTt3QkFBVSxJQUFJYjtnQkFJdkQsTUFBTTlDLFdBQVcsTUFBTUMsTUFBTSxzQkFBNEIsT0FBTnlDLFFBQVM7b0JBQzFEaEIsUUFBUTtnQkFDVjtnQkFFQSxJQUFJLENBQUMxQixTQUFTRSxFQUFFLEVBQUU7b0JBQ2hCLHdDQUF3QztvQkFDeENiLFdBQVd1RDtvQkFDWCxNQUFNckMsUUFBUSxNQUFNUCxTQUFTSyxJQUFJO29CQUNqQyxNQUFNLElBQUlGLE1BQU1JLE1BQU1BLEtBQUssSUFBSTtnQkFDakM7Z0JBRUE3Qix5Q0FBS0EsQ0FBQ3VELE9BQU8sQ0FBQztnQkFDZCw0Q0FBNEM7Z0JBQzVDLE1BQU1sQztZQUNSLEVBQUUsT0FBT1EsT0FBWTtnQkFDbkJDLFFBQVFELEtBQUssQ0FBQywyQkFBMkJBO2dCQUN6QzdCLHlDQUFLQSxDQUFDNkIsS0FBSyxDQUFDQSxNQUFNaUMsT0FBTyxJQUFJO2dCQUM3QixNQUFNakMsT0FBTyxnRUFBZ0U7WUFDL0U7UUFDRjtJQUVKO0lBRUEsTUFBTXFELGtCQUFrQixDQUFDbEI7UUFDdkIsTUFBTVEsU0FBUzlELFFBQVErRCxJQUFJLENBQUNMLENBQUFBLE1BQU9BLElBQUlWLEVBQUUsS0FBS007UUFDOUNNLGtCQUFrQkU7UUFDbEJXLG1CQUFtQjtJQUNyQjtJQUVBLE1BQU1DLDBCQUEwQixDQUFDQztRQUMvQnBFLG9CQUFvQm9FO0lBQ3BCLHNGQUFzRjtJQUN4RjtJQUVBLE1BQU1DLG9CQUFvQnBFLG1CQUN0QkEsaUJBQWlCMkIsWUFBWSxHQUFHM0IsaUJBQWlCMEIsUUFBUSxHQUN6RDtJQUVKLElBQUloQyxTQUFTO1FBQ1gscUJBQ0UsOERBQUNqQixxREFBSUE7c0JBQ0gsNEVBQUNDLDREQUFXQTtnQkFBQzJGLFdBQVU7O2tDQUNyQiw4REFBQ3hGLCtGQUFTQTt3QkFBQ3dGLFdBQVU7Ozs7OztvQkFBOEI7Ozs7Ozs7Ozs7OztJQUszRDtJQUVBLHFCQUNFLDhEQUFDQztRQUFJRCxXQUFVOzswQkFFYiw4REFBQ0M7Z0JBQUlELFdBQVU7O2tDQUNiLDhEQUFDQzs7MENBQ0MsOERBQUNDO2dDQUFHRixXQUFVOztrREFDWiw4REFBQ3pGLCtGQUFHQTt3Q0FBQ3lGLFdBQVU7Ozs7OztvQ0FBWTs7Ozs7OzswQ0FHN0IsOERBQUNHO2dDQUFFSCxXQUFVOztvQ0FBcUI7b0NBQ2M5RTs7Ozs7Ozs7Ozs7OztvQkFHakQ2RSxrQ0FDQyw4REFBQzVGLHlEQUFNQTt3QkFDTGlHLFNBQVMsSUFBTTFFLG9CQUFvQjt3QkFDbkNzRSxXQUFVOzswQ0FFViw4REFBQzFGLCtGQUFJQTtnQ0FBQzBGLFdBQVU7Ozs7Ozs0QkFBWTs7Ozs7OzZDQUk5Qiw4REFBQ0M7d0JBQUlELFdBQVU7OzBDQUNiLDhEQUFDN0YseURBQU1BO2dDQUNMa0csUUFBUTtnQ0FDUkwsV0FBVTs7a0RBRVYsOERBQUMxRiwrRkFBSUE7d0NBQUMwRixXQUFVOzs7Ozs7b0NBQVk7Ozs7Ozs7MENBRzlCLDhEQUFDRztnQ0FBRUgsV0FBVTswQ0FDVnJFLENBQUFBLDZCQUFBQSx1Q0FBQUEsaUJBQWtCaUIsSUFBSSxNQUFLLFNBQ3hCLDhDQUNBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFRWGpCLGtDQUNDLDhEQUFDc0U7Z0JBQUlELFdBQVU7MEJBQ2IsNEVBQUNsRix1RUFBY0E7b0JBQ2J3RixTQUFTM0UsaUJBQWlCMkIsWUFBWTtvQkFDdENpRCxPQUFPNUUsaUJBQWlCMEIsUUFBUTtvQkFDaENtRCxPQUFNO29CQUNONUQsTUFBTWpCLGlCQUFpQmlCLElBQUk7b0JBQzNCNkQsaUJBQWlCOzs7Ozs7Ozs7OztZQU10QnRGLFFBQVFpQyxNQUFNLEtBQUssa0JBQ2xCLDhEQUFDaEQscURBQUlBOzBCQUNILDRFQUFDQyw0REFBV0E7b0JBQUMyRixXQUFVOztzQ0FDckIsOERBQUN6RiwrRkFBR0E7NEJBQUN5RixXQUFVOzs7Ozs7c0NBQ2YsOERBQUNVOzRCQUFHVixXQUFVO3NDQUE2Qjs7Ozs7O3NDQUMzQyw4REFBQ0c7NEJBQUVILFdBQVU7c0NBQXFCOzs7Ozs7d0JBR2pDRCxrQ0FDQyw4REFBQzVGLHlEQUFNQTs0QkFDTGlHLFNBQVMsSUFBTTFFLG9CQUFvQjs7OENBRW5DLDhEQUFDcEIsK0ZBQUlBO29DQUFDMEYsV0FBVTs7Ozs7O2dDQUFpQjs7Ozs7O2lEQUluQyw4REFBQ0M7NEJBQUlELFdBQVU7OzhDQUNiLDhEQUFDN0YseURBQU1BO29DQUFDa0csUUFBUTtvQ0FBQ0wsV0FBVTs7c0RBQ3pCLDhEQUFDMUYsK0ZBQUlBOzRDQUFDMEYsV0FBVTs7Ozs7O3dDQUFpQjs7Ozs7Ozs4Q0FHbkMsOERBQUNHO29DQUFFSCxXQUFVOzhDQUNWckUsQ0FBQUEsNkJBQUFBLHVDQUFBQSxpQkFBa0JpQixJQUFJLE1BQUssU0FDeEIsK0NBQ0E7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7cUNBUWQsOERBQUNxRDtnQkFBSUQsV0FBVTswQkFDWjdFLFFBQVF5RCxHQUFHLENBQUMsQ0FBQ0ssdUJBQ1osOERBQUN2RSxtREFBVUE7d0JBRVR1RSxRQUFRQTt3QkFDUjBCLFFBQVEsQ0FBQzlCOzRCQUNQRSxrQkFBa0JGOzRCQUNsQkMsa0JBQWtCO3dCQUNwQjt3QkFDQThCLFVBQVU1Qjt3QkFDVjZCLGFBQWFsQjt1QkFQUlYsT0FBT2QsRUFBRTs7Ozs7Ozs7OzswQkFjdEIsOERBQUN4RCxtRUFBa0JBO2dCQUNqQm1GLE1BQU1yRTtnQkFDTnFGLGNBQWNqQjtnQkFDZGtCLGdCQUFnQnhEO2dCQUNoQnJDLFlBQVlBO2dCQUNaSyxVQUFVQTtnQkFDVnlGLGtCQUFrQnJGLENBQUFBLDZCQUFBQSx1Q0FBQUEsaUJBQWtCaUIsSUFBSSxLQUFJOzs7Ozs7WUFHN0NxRSxnQ0FDQzs7a0NBQ0UsOERBQUNyRywrREFBZ0JBO3dCQUNma0YsTUFBTW9CO3dCQUNOSixjQUFjaEM7d0JBQ2RHLFFBQVFnQzt3QkFDUkUsZ0JBQWdCLENBQUN6QyxVQUFZRixpQkFBaUJ5QyxlQUFlOUMsRUFBRSxFQUFFTzs7Ozs7O2tDQUduRSw4REFBQzdELGlFQUFpQkE7d0JBQ2hCaUYsTUFBTXNCO3dCQUNOTixjQUFjbEI7d0JBQ2RYLFFBQVFnQzs7Ozs7Ozs7Ozs7Ozs7QUFNcEI7R0FoWGdCakc7O1FBV09ELG9FQUFlQTs7O0tBWHRCQyIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxzcmNcXGNvbXBvbmVudHNcXFVzZXJBcGlLZXlzXFxBcGlLZXlNYW5hZ2VyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL0J1dHRvbic7XG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCwgQ2FyZEhlYWRlciwgQ2FyZFRpdGxlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL0NhcmQnO1xuaW1wb3J0IHsgQmFkZ2UgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYmFkZ2UnO1xuaW1wb3J0IHsgUGx1cywgS2V5LCBBbGVydENpcmNsZSwgUmVmcmVzaEN3IH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcbmltcG9ydCB7IHRvYXN0IH0gZnJvbSAnc29ubmVyJztcbmltcG9ydCB7IEFwaUtleUNhcmQgfSBmcm9tICcuL0FwaUtleUNhcmQnO1xuaW1wb3J0IHsgQ3JlYXRlQXBpS2V5RGlhbG9nIH0gZnJvbSAnLi9DcmVhdGVBcGlLZXlEaWFsb2cnO1xuaW1wb3J0IHsgRWRpdEFwaUtleURpYWxvZyB9IGZyb20gJy4vRWRpdEFwaUtleURpYWxvZyc7XG5pbXBvcnQgeyBBcGlLZXlVc2FnZURpYWxvZyB9IGZyb20gJy4vQXBpS2V5VXNhZ2VEaWFsb2cnO1xuaW1wb3J0IHsgQWxlcnQsIEFsZXJ0RGVzY3JpcHRpb24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYWxlcnQnO1xuaW1wb3J0IHsgTGltaXRJbmRpY2F0b3IgfSBmcm9tICdAL2NvbXBvbmVudHMvVGllckVuZm9yY2VtZW50JztcbmltcG9ydCBDb25maXJtYXRpb25Nb2RhbCBmcm9tICdAL2NvbXBvbmVudHMvdWkvQ29uZmlybWF0aW9uTW9kYWwnO1xuaW1wb3J0IHsgdXNlQ29uZmlybWF0aW9uIH0gZnJvbSAnQC9ob29rcy91c2VDb25maXJtYXRpb24nO1xuXG5pbnRlcmZhY2UgQXBpS2V5TWFuYWdlclByb3BzIHtcbiAgY29uZmlnSWQ6IHN0cmluZztcbiAgY29uZmlnTmFtZTogc3RyaW5nO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gQXBpS2V5TWFuYWdlcih7IGNvbmZpZ0lkLCBjb25maWdOYW1lIH06IEFwaUtleU1hbmFnZXJQcm9wcykge1xuICBjb25zdCBbYXBpS2V5cywgc2V0QXBpS2V5c10gPSB1c2VTdGF0ZTxhbnlbXT4oW10pO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW2NyZWF0aW5nLCBzZXRDcmVhdGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzaG93Q3JlYXRlRGlhbG9nLCBzZXRTaG93Q3JlYXRlRGlhbG9nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3N1YnNjcmlwdGlvbkluZm8sIHNldFN1YnNjcmlwdGlvbkluZm9dID0gdXNlU3RhdGU8e1xuICAgIHRpZXI6IHN0cmluZztcbiAgICBrZXlMaW1pdDogbnVtYmVyO1xuICAgIGN1cnJlbnRDb3VudDogbnVtYmVyO1xuICB9IHwgbnVsbD4obnVsbCk7XG5cbiAgY29uc3QgY29uZmlybWF0aW9uID0gdXNlQ29uZmlybWF0aW9uKCk7XG5cbiAgLy8gRmV0Y2ggQVBJIGtleXMgZm9yIHRoaXMgY29uZmlndXJhdGlvblxuICBjb25zdCBmZXRjaEFwaUtleXMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAvYXBpL3VzZXItYXBpLWtleXM/Y29uZmlnX2lkPSR7Y29uZmlnSWR9YCk7XG4gICAgICBcbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gZmV0Y2ggQVBJIGtleXMnKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIHNldEFwaUtleXMoZGF0YS5hcGlfa2V5cyB8fCBbXSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIEFQSSBrZXlzOicsIGVycm9yKTtcbiAgICAgIHRvYXN0LmVycm9yKCdGYWlsZWQgdG8gbG9hZCBBUEkga2V5cycpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gRmV0Y2ggc3Vic2NyaXB0aW9uIGluZm9cbiAgY29uc3QgZmV0Y2hTdWJzY3JpcHRpb25JbmZvID0gYXN5bmMgKGN1cnJlbnRLZXlDb3VudD86IG51bWJlcikgPT4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBHZXQgdGhlIHRpZXIgZnJvbSB0aGUgdXNlcidzIGFjdGl2ZSBzdWJzY3JpcHRpb25cbiAgICAgIGNvbnN0IHRpZXJSZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL3VzZXIvc3Vic2NyaXB0aW9uLXRpZXInKTtcbiAgICAgIGNvbnN0IHRpZXJEYXRhID0gdGllclJlc3BvbnNlLm9rID8gYXdhaXQgdGllclJlc3BvbnNlLmpzb24oKSA6IG51bGw7XG4gICAgICBjb25zdCB0aWVyID0gdGllckRhdGE/LnRpZXIgfHwgJ3N0YXJ0ZXInO1xuXG4gICAgICBjb25zdCBsaW1pdHMgPSB7XG4gICAgICAgIGZyZWU6IDMsXG4gICAgICAgIHN0YXJ0ZXI6IDUwLFxuICAgICAgICBwcm9mZXNzaW9uYWw6IDk5OTk5OSxcbiAgICAgICAgZW50ZXJwcmlzZTogOTk5OTk5XG4gICAgICB9O1xuXG4gICAgICAvLyBVc2UgcHJvdmlkZWQgY291bnQgb3IgY3VycmVudCBhcGlLZXlzIGxlbmd0aFxuICAgICAgY29uc3Qga2V5Q291bnQgPSBjdXJyZW50S2V5Q291bnQgIT09IHVuZGVmaW5lZCA/IGN1cnJlbnRLZXlDb3VudCA6IGFwaUtleXMubGVuZ3RoO1xuXG4gICAgICBzZXRTdWJzY3JpcHRpb25JbmZvKHtcbiAgICAgICAgdGllcixcbiAgICAgICAga2V5TGltaXQ6IGxpbWl0c1t0aWVyIGFzIGtleW9mIHR5cGVvZiBsaW1pdHNdIHx8IGxpbWl0cy5mcmVlLFxuICAgICAgICBjdXJyZW50Q291bnQ6IGtleUNvdW50XG4gICAgICB9KTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgc3Vic2NyaXB0aW9uIGluZm86JywgZXJyb3IpO1xuICAgICAgLy8gRmFsbGJhY2sgdG8gZnJlZSB0aWVyXG4gICAgICBjb25zdCBrZXlDb3VudCA9IGN1cnJlbnRLZXlDb3VudCAhPT0gdW5kZWZpbmVkID8gY3VycmVudEtleUNvdW50IDogYXBpS2V5cy5sZW5ndGg7XG4gICAgICBzZXRTdWJzY3JpcHRpb25JbmZvKHtcbiAgICAgICAgdGllcjogJ2ZyZWUnLFxuICAgICAgICBrZXlMaW1pdDogMyxcbiAgICAgICAgY3VycmVudENvdW50OiBrZXlDb3VudFxuICAgICAgfSk7XG4gICAgfVxuICB9O1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgZmV0Y2hBcGlLZXlzKCk7XG4gIH0sIFtjb25maWdJZF0pO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGFwaUtleXMubGVuZ3RoID49IDApIHtcbiAgICAgIGZldGNoU3Vic2NyaXB0aW9uSW5mbygpO1xuICAgIH1cbiAgfSwgW2FwaUtleXNdKTtcblxuICBjb25zdCBoYW5kbGVDcmVhdGVBcGlLZXkgPSBhc3luYyAoa2V5RGF0YTogYW55KSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldENyZWF0aW5nKHRydWUpO1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS91c2VyLWFwaS1rZXlzJywge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgICAuLi5rZXlEYXRhLFxuICAgICAgICAgIGN1c3RvbV9hcGlfY29uZmlnX2lkOiBjb25maWdJZCxcbiAgICAgICAgfSksXG4gICAgICB9KTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBlcnJvciA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yLmVycm9yIHx8ICdGYWlsZWQgdG8gY3JlYXRlIEFQSSBrZXknKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgbmV3QXBpS2V5ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuXG4gICAgICAvLyBTaG93IHRoZSBmdWxsIEFQSSBrZXkgaW4gYSBzcGVjaWFsIGRpYWxvZyBzaW5jZSBpdCdzIG9ubHkgc2hvd24gb25jZVxuICAgICAgdG9hc3Quc3VjY2VzcygnQVBJIGtleSBjcmVhdGVkIHN1Y2Nlc3NmdWxseSEnKTtcblxuICAgICAgLy8gT3B0aW1pc3RpY2FsbHkgYWRkIHRoZSBuZXcga2V5IHRvIHRoZSBsaXN0IGltbWVkaWF0ZWx5XG4gICAgICBjb25zdCBvcHRpbWlzdGljS2V5ID0ge1xuICAgICAgICAuLi5uZXdBcGlLZXksXG4gICAgICAgIC8vIEFkZCBhbnkgbWlzc2luZyBmaWVsZHMgdGhhdCBtaWdodCBiZSBuZWVkZWQgZm9yIGRpc3BsYXlcbiAgICAgICAgY3VzdG9tX2FwaV9jb25maWdzOiB7XG4gICAgICAgICAgaWQ6IGNvbmZpZ0lkLFxuICAgICAgICAgIG5hbWU6IGNvbmZpZ05hbWVcbiAgICAgICAgfVxuICAgICAgfTtcbiAgICAgIHNldEFwaUtleXMocHJldktleXMgPT4ge1xuICAgICAgICBjb25zdCBuZXdLZXlzID0gW29wdGltaXN0aWNLZXksIC4uLnByZXZLZXlzXTtcbiAgICAgICAgLy8gSW1tZWRpYXRlbHkgdXBkYXRlIHN1YnNjcmlwdGlvbiBpbmZvIHdpdGggbmV3IGNvdW50XG4gICAgICAgIGZldGNoU3Vic2NyaXB0aW9uSW5mbyhuZXdLZXlzLmxlbmd0aCk7XG4gICAgICAgIHJldHVybiBuZXdLZXlzO1xuICAgICAgfSk7XG5cbiAgICAgIC8vIEFsc28gcmVmcmVzaCB0byBlbnN1cmUgd2UgaGF2ZSB0aGUgbGF0ZXN0IGRhdGEgYW5kIGNvcnJlY3QgY291bnRzXG4gICAgICBhd2FpdCBmZXRjaEFwaUtleXMoKTtcblxuICAgICAgcmV0dXJuIG5ld0FwaUtleTtcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjcmVhdGluZyBBUEkga2V5OicsIGVycm9yKTtcbiAgICAgIHRvYXN0LmVycm9yKGVycm9yLm1lc3NhZ2UgfHwgJ0ZhaWxlZCB0byBjcmVhdGUgQVBJIGtleScpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldENyZWF0aW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRWRpdEFwaUtleSA9IGFzeW5jIChrZXlJZDogc3RyaW5nLCB1cGRhdGVzOiBhbnkpID0+IHtcbiAgICB0cnkge1xuICAgICAgLy8gT3B0aW1pc3RpY2FsbHkgdXBkYXRlIHRoZSBrZXkgaW4gdGhlIGxpc3RcbiAgICAgIGNvbnN0IHByZXZpb3VzS2V5cyA9IFsuLi5hcGlLZXlzXTtcbiAgICAgIHNldEFwaUtleXMocHJldktleXMgPT5cbiAgICAgICAgcHJldktleXMubWFwKGtleSA9PlxuICAgICAgICAgIGtleS5pZCA9PT0ga2V5SWQgPyB7IC4uLmtleSwgLi4udXBkYXRlcyB9IDoga2V5XG4gICAgICAgIClcbiAgICAgICk7XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvdXNlci1hcGkta2V5cy8ke2tleUlkfWAsIHtcbiAgICAgICAgbWV0aG9kOiAnUEFUQ0gnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkodXBkYXRlcyksXG4gICAgICB9KTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICAvLyBSZXZlcnQgdGhlIG9wdGltaXN0aWMgdXBkYXRlIG9uIGVycm9yXG4gICAgICAgIHNldEFwaUtleXMocHJldmlvdXNLZXlzKTtcbiAgICAgICAgY29uc3QgZXJyb3IgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvci5lcnJvciB8fCAnRmFpbGVkIHRvIHVwZGF0ZSBBUEkga2V5Jyk7XG4gICAgICB9XG5cbiAgICAgIHRvYXN0LnN1Y2Nlc3MoJ0FQSSBrZXkgdXBkYXRlZCBzdWNjZXNzZnVsbHknKTtcbiAgICAgIC8vIFJlZnJlc2ggdG8gZW5zdXJlIHdlIGhhdmUgdGhlIGxhdGVzdCBkYXRhXG4gICAgICBhd2FpdCBmZXRjaEFwaUtleXMoKTtcbiAgICAgIHNldFNob3dFZGl0RGlhbG9nKGZhbHNlKTtcbiAgICAgIHNldFNlbGVjdGVkQXBpS2V5KG51bGwpO1xuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHVwZGF0aW5nIEFQSSBrZXk6JywgZXJyb3IpO1xuICAgICAgdG9hc3QuZXJyb3IoZXJyb3IubWVzc2FnZSB8fCAnRmFpbGVkIHRvIHVwZGF0ZSBBUEkga2V5Jyk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVJldm9rZUFwaUtleSA9IGFzeW5jIChrZXlJZDogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgYXBpS2V5ID0gYXBpS2V5cy5maW5kKGtleSA9PiBrZXkuaWQgPT09IGtleUlkKTtcbiAgICBjb25zdCBrZXlOYW1lID0gYXBpS2V5Py5rZXlfbmFtZSB8fCAndGhpcyBBUEkga2V5JztcblxuICAgIGNvbmZpcm1hdGlvbi5zaG93Q29uZmlybWF0aW9uKFxuICAgICAge1xuICAgICAgICB0aXRsZTogJ1Jldm9rZSBBUEkgS2V5JyxcbiAgICAgICAgbWVzc2FnZTogYEFyZSB5b3Ugc3VyZSB5b3Ugd2FudCB0byByZXZva2UgXCIke2tleU5hbWV9XCI/IFRoaXMgYWN0aW9uIGNhbm5vdCBiZSB1bmRvbmUgYW5kIHdpbGwgaW1tZWRpYXRlbHkgZGlzYWJsZSB0aGUga2V5LmAsXG4gICAgICAgIGNvbmZpcm1UZXh0OiAnUmV2b2tlIEtleScsXG4gICAgICAgIGNhbmNlbFRleHQ6ICdDYW5jZWwnLFxuICAgICAgICB0eXBlOiAnZGFuZ2VyJ1xuICAgICAgfSxcbiAgICAgIGFzeW5jICgpID0+IHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAvLyBPcHRpbWlzdGljYWxseSB1cGRhdGUgdGhlIGtleSBzdGF0dXMgdG8gcmV2b2tlZCBpbnN0ZWFkIG9mIHJlbW92aW5nIGl0XG4gICAgICAgICAgY29uc3QgcHJldmlvdXNLZXlzID0gWy4uLmFwaUtleXNdO1xuICAgICAgICAgIHNldEFwaUtleXMocHJldktleXMgPT5cbiAgICAgICAgICAgIHByZXZLZXlzLm1hcChrZXkgPT5cbiAgICAgICAgICAgICAga2V5LmlkID09PSBrZXlJZCA/IHsgLi4ua2V5LCBzdGF0dXM6ICdyZXZva2VkJyB9IDoga2V5XG4gICAgICAgICAgICApXG4gICAgICAgICAgKTtcblxuICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvdXNlci1hcGkta2V5cy8ke2tleUlkfWAsIHtcbiAgICAgICAgICAgIG1ldGhvZDogJ0RFTEVURScsXG4gICAgICAgICAgfSk7XG5cbiAgICAgICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgICAgICAvLyBSZXZlcnQgdGhlIG9wdGltaXN0aWMgdXBkYXRlIG9uIGVycm9yXG4gICAgICAgICAgICBzZXRBcGlLZXlzKHByZXZpb3VzS2V5cyk7XG4gICAgICAgICAgICBjb25zdCBlcnJvciA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvci5lcnJvciB8fCAnRmFpbGVkIHRvIHJldm9rZSBBUEkga2V5Jyk7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgdG9hc3Quc3VjY2VzcygnQVBJIGtleSByZXZva2VkIHN1Y2Nlc3NmdWxseScpO1xuICAgICAgICAgIC8vIFJlZnJlc2ggdG8gZW5zdXJlIHdlIGhhdmUgdGhlIGxhdGVzdCBkYXRhXG4gICAgICAgICAgYXdhaXQgZmV0Y2hBcGlLZXlzKCk7XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciByZXZva2luZyBBUEkga2V5OicsIGVycm9yKTtcbiAgICAgICAgICB0b2FzdC5lcnJvcihlcnJvci5tZXNzYWdlIHx8ICdGYWlsZWQgdG8gcmV2b2tlIEFQSSBrZXknKTtcbiAgICAgICAgICB0aHJvdyBlcnJvcjsgLy8gUmUtdGhyb3cgdG8gbGV0IHRoZSBjb25maXJtYXRpb24gbW9kYWwgaGFuZGxlIHRoZSBlcnJvciBzdGF0ZVxuICAgICAgICB9XG4gICAgICB9XG4gICAgKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVWaWV3VXNhZ2UgPSAoa2V5SWQ6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IGFwaUtleSA9IGFwaUtleXMuZmluZChrZXkgPT4ga2V5LmlkID09PSBrZXlJZCk7XG4gICAgc2V0U2VsZWN0ZWRBcGlLZXkoYXBpS2V5KTtcbiAgICBzZXRTaG93VXNhZ2VEaWFsb2codHJ1ZSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQ3JlYXRlRGlhbG9nQ2xvc2UgPSAob3BlbjogYm9vbGVhbikgPT4ge1xuICAgIHNldFNob3dDcmVhdGVEaWFsb2cob3Blbik7XG4gICAgLy8gTm90ZTogV2Ugbm8gbG9uZ2VyIG5lZWQgdG8gcmVmcmVzaCBoZXJlIHNpbmNlIHdlIHJlZnJlc2ggaW1tZWRpYXRlbHkgYWZ0ZXIgY3JlYXRpb25cbiAgfTtcblxuICBjb25zdCBjYW5DcmVhdGVNb3JlS2V5cyA9IHN1YnNjcmlwdGlvbkluZm8gXG4gICAgPyBzdWJzY3JpcHRpb25JbmZvLmN1cnJlbnRDb3VudCA8IHN1YnNjcmlwdGlvbkluZm8ua2V5TGltaXQgXG4gICAgOiB0cnVlO1xuXG4gIGlmIChsb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxDYXJkPlxuICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcHktOFwiPlxuICAgICAgICAgIDxSZWZyZXNoQ3cgY2xhc3NOYW1lPVwiaC02IHctNiBhbmltYXRlLXNwaW4gbXItMlwiIC8+XG4gICAgICAgICAgTG9hZGluZyBBUEkga2V5cy4uLlxuICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgPC9DYXJkPlxuICAgICk7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICB7LyogSGVhZGVyICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICA8S2V5IGNsYXNzTmFtZT1cImgtNiB3LTZcIiAvPlxuICAgICAgICAgICAgQVBJIEtleXNcbiAgICAgICAgICA8L2gyPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbXQtMVwiPlxuICAgICAgICAgICAgR2VuZXJhdGUgQVBJIGtleXMgZm9yIHByb2dyYW1tYXRpYyBhY2Nlc3MgdG8ge2NvbmZpZ05hbWV9XG4gICAgICAgICAgPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAge2NhbkNyZWF0ZU1vcmVLZXlzID8gKFxuICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dDcmVhdGVEaWFsb2codHJ1ZSl9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFBsdXMgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICBDcmVhdGUgQVBJIEtleVxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICApIDogKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1lbmQgZ2FwLTJcIj5cbiAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgZGlzYWJsZWRcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgb3BhY2l0eS01MFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICBDcmVhdGUgQVBJIEtleVxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtb3JhbmdlLTYwMCBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICB7c3Vic2NyaXB0aW9uSW5mbz8udGllciA9PT0gJ2ZyZWUnXG4gICAgICAgICAgICAgICAgPyAnVXBncmFkZSB0byBTdGFydGVyIHBsYW4gZm9yIG1vcmUgQVBJIGtleXMnXG4gICAgICAgICAgICAgICAgOiAnQVBJIGtleSBsaW1pdCByZWFjaGVkIC0gdXBncmFkZSBmb3IgdW5saW1pdGVkIGtleXMnXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogQVBJIEtleSBVc2FnZSBMaW1pdHMgKi99XG4gICAgICB7c3Vic2NyaXB0aW9uSW5mbyAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHAtNFwiPlxuICAgICAgICAgIDxMaW1pdEluZGljYXRvclxuICAgICAgICAgICAgY3VycmVudD17c3Vic2NyaXB0aW9uSW5mby5jdXJyZW50Q291bnR9XG4gICAgICAgICAgICBsaW1pdD17c3Vic2NyaXB0aW9uSW5mby5rZXlMaW1pdH1cbiAgICAgICAgICAgIGxhYmVsPVwiVXNlci1HZW5lcmF0ZWQgQVBJIEtleXNcIlxuICAgICAgICAgICAgdGllcj17c3Vic2NyaXB0aW9uSW5mby50aWVyIGFzIGFueX1cbiAgICAgICAgICAgIHNob3dVcGdyYWRlSGludD17dHJ1ZX1cbiAgICAgICAgICAvPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBBUEkgS2V5cyBMaXN0ICovfVxuICAgICAge2FwaUtleXMubGVuZ3RoID09PSAwID8gKFxuICAgICAgICA8Q2FyZD5cbiAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktOFwiPlxuICAgICAgICAgICAgPEtleSBjbGFzc05hbWU9XCJoLTEyIHctMTIgbXgtYXV0byB0ZXh0LWdyYXktNDAwIG1iLTRcIiAvPlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCBtYi0yXCI+Tm8gQVBJIEtleXM8L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtYi00XCI+XG4gICAgICAgICAgICAgIENyZWF0ZSB5b3VyIGZpcnN0IEFQSSBrZXkgdG8gc3RhcnQgdXNpbmcgdGhlIFJvdUtleSBBUEkgcHJvZ3JhbW1hdGljYWxseS5cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIHtjYW5DcmVhdGVNb3JlS2V5cyA/IChcbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dDcmVhdGVEaWFsb2codHJ1ZSl9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8UGx1cyBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgIENyZWF0ZSBZb3VyIEZpcnN0IEFQSSBLZXlcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgPEJ1dHRvbiBkaXNhYmxlZCBjbGFzc05hbWU9XCJvcGFjaXR5LTUwXCI+XG4gICAgICAgICAgICAgICAgICA8UGx1cyBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgQ3JlYXRlIFlvdXIgRmlyc3QgQVBJIEtleVxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1vcmFuZ2UtNjAwIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICB7c3Vic2NyaXB0aW9uSW5mbz8udGllciA9PT0gJ2ZyZWUnXG4gICAgICAgICAgICAgICAgICAgID8gJ1VwZ3JhZGUgdG8gU3RhcnRlciBwbGFuIHRvIGNyZWF0ZSBBUEkga2V5cydcbiAgICAgICAgICAgICAgICAgICAgOiAnQVBJIGtleSBsaW1pdCByZWFjaGVkIC0gdXBncmFkZSBmb3IgdW5saW1pdGVkIGtleXMnXG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgPC9DYXJkPlxuICAgICAgKSA6IChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdhcC00XCI+XG4gICAgICAgICAge2FwaUtleXMubWFwKChhcGlLZXkpID0+IChcbiAgICAgICAgICAgIDxBcGlLZXlDYXJkXG4gICAgICAgICAgICAgIGtleT17YXBpS2V5LmlkfVxuICAgICAgICAgICAgICBhcGlLZXk9e2FwaUtleX1cbiAgICAgICAgICAgICAgb25FZGl0PXsoa2V5KSA9PiB7XG4gICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRBcGlLZXkoa2V5KTtcbiAgICAgICAgICAgICAgICBzZXRTaG93RWRpdERpYWxvZyh0cnVlKTtcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgb25SZXZva2U9e2hhbmRsZVJldm9rZUFwaUtleX1cbiAgICAgICAgICAgICAgb25WaWV3VXNhZ2U9e2hhbmRsZVZpZXdVc2FnZX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgey8qIERpYWxvZ3MgKi99XG4gICAgICA8Q3JlYXRlQXBpS2V5RGlhbG9nXG4gICAgICAgIG9wZW49e3Nob3dDcmVhdGVEaWFsb2d9XG4gICAgICAgIG9uT3BlbkNoYW5nZT17aGFuZGxlQ3JlYXRlRGlhbG9nQ2xvc2V9XG4gICAgICAgIG9uQ3JlYXRlQXBpS2V5PXtoYW5kbGVDcmVhdGVBcGlLZXl9XG4gICAgICAgIGNvbmZpZ05hbWU9e2NvbmZpZ05hbWV9XG4gICAgICAgIGNyZWF0aW5nPXtjcmVhdGluZ31cbiAgICAgICAgc3Vic2NyaXB0aW9uVGllcj17c3Vic2NyaXB0aW9uSW5mbz8udGllciB8fCAnc3RhcnRlcid9XG4gICAgICAvPlxuXG4gICAgICB7c2VsZWN0ZWRBcGlLZXkgJiYgKFxuICAgICAgICA8PlxuICAgICAgICAgIDxFZGl0QXBpS2V5RGlhbG9nXG4gICAgICAgICAgICBvcGVuPXtzaG93RWRpdERpYWxvZ31cbiAgICAgICAgICAgIG9uT3BlbkNoYW5nZT17c2V0U2hvd0VkaXREaWFsb2d9XG4gICAgICAgICAgICBhcGlLZXk9e3NlbGVjdGVkQXBpS2V5fVxuICAgICAgICAgICAgb25VcGRhdGVBcGlLZXk9eyh1cGRhdGVzKSA9PiBoYW5kbGVFZGl0QXBpS2V5KHNlbGVjdGVkQXBpS2V5LmlkLCB1cGRhdGVzKX1cbiAgICAgICAgICAvPlxuXG4gICAgICAgICAgPEFwaUtleVVzYWdlRGlhbG9nXG4gICAgICAgICAgICBvcGVuPXtzaG93VXNhZ2VEaWFsb2d9XG4gICAgICAgICAgICBvbk9wZW5DaGFuZ2U9e3NldFNob3dVc2FnZURpYWxvZ31cbiAgICAgICAgICAgIGFwaUtleT17c2VsZWN0ZWRBcGlLZXl9XG4gICAgICAgICAgLz5cbiAgICAgICAgPC8+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJCdXR0b24iLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJQbHVzIiwiS2V5IiwiUmVmcmVzaEN3IiwidG9hc3QiLCJBcGlLZXlDYXJkIiwiQ3JlYXRlQXBpS2V5RGlhbG9nIiwiRWRpdEFwaUtleURpYWxvZyIsIkFwaUtleVVzYWdlRGlhbG9nIiwiTGltaXRJbmRpY2F0b3IiLCJ1c2VDb25maXJtYXRpb24iLCJBcGlLZXlNYW5hZ2VyIiwiY29uZmlnSWQiLCJjb25maWdOYW1lIiwiYXBpS2V5cyIsInNldEFwaUtleXMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImNyZWF0aW5nIiwic2V0Q3JlYXRpbmciLCJzaG93Q3JlYXRlRGlhbG9nIiwic2V0U2hvd0NyZWF0ZURpYWxvZyIsInN1YnNjcmlwdGlvbkluZm8iLCJzZXRTdWJzY3JpcHRpb25JbmZvIiwiY29uZmlybWF0aW9uIiwiZmV0Y2hBcGlLZXlzIiwicmVzcG9uc2UiLCJmZXRjaCIsIm9rIiwiRXJyb3IiLCJkYXRhIiwianNvbiIsImFwaV9rZXlzIiwiZXJyb3IiLCJjb25zb2xlIiwiZmV0Y2hTdWJzY3JpcHRpb25JbmZvIiwiY3VycmVudEtleUNvdW50IiwidGllclJlc3BvbnNlIiwidGllckRhdGEiLCJ0aWVyIiwibGltaXRzIiwiZnJlZSIsInN0YXJ0ZXIiLCJwcm9mZXNzaW9uYWwiLCJlbnRlcnByaXNlIiwia2V5Q291bnQiLCJ1bmRlZmluZWQiLCJsZW5ndGgiLCJrZXlMaW1pdCIsImN1cnJlbnRDb3VudCIsImhhbmRsZUNyZWF0ZUFwaUtleSIsImtleURhdGEiLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJjdXN0b21fYXBpX2NvbmZpZ19pZCIsIm5ld0FwaUtleSIsInN1Y2Nlc3MiLCJvcHRpbWlzdGljS2V5IiwiY3VzdG9tX2FwaV9jb25maWdzIiwiaWQiLCJuYW1lIiwicHJldktleXMiLCJuZXdLZXlzIiwibWVzc2FnZSIsImhhbmRsZUVkaXRBcGlLZXkiLCJrZXlJZCIsInVwZGF0ZXMiLCJwcmV2aW91c0tleXMiLCJtYXAiLCJrZXkiLCJzZXRTaG93RWRpdERpYWxvZyIsInNldFNlbGVjdGVkQXBpS2V5IiwiaGFuZGxlUmV2b2tlQXBpS2V5IiwiYXBpS2V5IiwiZmluZCIsImtleU5hbWUiLCJrZXlfbmFtZSIsInNob3dDb25maXJtYXRpb24iLCJ0aXRsZSIsImNvbmZpcm1UZXh0IiwiY2FuY2VsVGV4dCIsInR5cGUiLCJzdGF0dXMiLCJoYW5kbGVWaWV3VXNhZ2UiLCJzZXRTaG93VXNhZ2VEaWFsb2ciLCJoYW5kbGVDcmVhdGVEaWFsb2dDbG9zZSIsIm9wZW4iLCJjYW5DcmVhdGVNb3JlS2V5cyIsImNsYXNzTmFtZSIsImRpdiIsImgyIiwicCIsIm9uQ2xpY2siLCJkaXNhYmxlZCIsImN1cnJlbnQiLCJsaW1pdCIsImxhYmVsIiwic2hvd1VwZ3JhZGVIaW50IiwiaDMiLCJvbkVkaXQiLCJvblJldm9rZSIsIm9uVmlld1VzYWdlIiwib25PcGVuQ2hhbmdlIiwib25DcmVhdGVBcGlLZXkiLCJzdWJzY3JpcHRpb25UaWVyIiwic2VsZWN0ZWRBcGlLZXkiLCJzaG93RWRpdERpYWxvZyIsIm9uVXBkYXRlQXBpS2V5Iiwic2hvd1VzYWdlRGlhbG9nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/UserApiKeys/ApiKeyManager.tsx\n"));

/***/ })

});