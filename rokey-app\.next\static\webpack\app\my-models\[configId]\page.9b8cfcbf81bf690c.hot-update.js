"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/[configId]/page",{

/***/ "(app-pages-browser)/./src/components/UserApiKeys/ApiKeyManager.tsx":
/*!******************************************************!*\
  !*** ./src/components/UserApiKeys/ApiKeyManager.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiKeyManager: () => (/* binding */ ApiKeyManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _ApiKeyCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ApiKeyCard */ \"(app-pages-browser)/./src/components/UserApiKeys/ApiKeyCard.tsx\");\n/* harmony import */ var _CreateApiKeyDialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CreateApiKeyDialog */ \"(app-pages-browser)/./src/components/UserApiKeys/CreateApiKeyDialog.tsx\");\n/* harmony import */ var _EditApiKeyDialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./EditApiKeyDialog */ \"(app-pages-browser)/./src/components/UserApiKeys/EditApiKeyDialog.tsx\");\n/* harmony import */ var _ApiKeyUsageDialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ApiKeyUsageDialog */ \"(app-pages-browser)/./src/components/UserApiKeys/ApiKeyUsageDialog.tsx\");\n/* harmony import */ var _components_TierEnforcement__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/TierEnforcement */ \"(app-pages-browser)/./src/components/TierEnforcement/index.ts\");\n/* __next_internal_client_entry_do_not_use__ ApiKeyManager auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction ApiKeyManager(param) {\n    let { configId, configName } = param;\n    _s();\n    const [apiKeys, setApiKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [creating, setCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCreateDialog, setShowCreateDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditDialog, setShowEditDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showUsageDialog, setShowUsageDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedApiKey, setSelectedApiKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [subscriptionInfo, setSubscriptionInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch API keys for this configuration\n    const fetchApiKeys = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/user-api-keys?config_id=\".concat(configId));\n            if (!response.ok) {\n                throw new Error('Failed to fetch API keys');\n            }\n            const data = await response.json();\n            setApiKeys(data.api_keys || []);\n        } catch (error) {\n            console.error('Error fetching API keys:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error('Failed to load API keys');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Fetch subscription info\n    const fetchSubscriptionInfo = async (currentKeyCount)=>{\n        try {\n            // Get the tier from the user's active subscription\n            const tierResponse = await fetch('/api/user/subscription-tier');\n            const tierData = tierResponse.ok ? await tierResponse.json() : null;\n            const tier = (tierData === null || tierData === void 0 ? void 0 : tierData.tier) || 'starter';\n            const limits = {\n                free: 3,\n                starter: 50,\n                professional: 999999,\n                enterprise: 999999\n            };\n            // Use provided count or current apiKeys length\n            const keyCount = currentKeyCount !== undefined ? currentKeyCount : apiKeys.length;\n            setSubscriptionInfo({\n                tier,\n                keyLimit: limits[tier] || limits.free,\n                currentCount: keyCount\n            });\n        } catch (error) {\n            console.error('Error fetching subscription info:', error);\n            // Fallback to free tier\n            const keyCount = currentKeyCount !== undefined ? currentKeyCount : apiKeys.length;\n            setSubscriptionInfo({\n                tier: 'free',\n                keyLimit: 3,\n                currentCount: keyCount\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ApiKeyManager.useEffect\": ()=>{\n            fetchApiKeys();\n        }\n    }[\"ApiKeyManager.useEffect\"], [\n        configId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ApiKeyManager.useEffect\": ()=>{\n            if (apiKeys.length >= 0) {\n                fetchSubscriptionInfo();\n            }\n        }\n    }[\"ApiKeyManager.useEffect\"], [\n        apiKeys\n    ]);\n    const handleCreateApiKey = async (keyData)=>{\n        try {\n            setCreating(true);\n            const response = await fetch('/api/user-api-keys', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...keyData,\n                    custom_api_config_id: configId\n                })\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                throw new Error(error.error || 'Failed to create API key');\n            }\n            const newApiKey = await response.json();\n            // Show the full API key in a special dialog since it's only shown once\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success('API key created successfully!');\n            // Optimistically add the new key to the list immediately\n            const optimisticKey = {\n                ...newApiKey,\n                // Add any missing fields that might be needed for display\n                custom_api_configs: {\n                    id: configId,\n                    name: configName\n                }\n            };\n            setApiKeys((prevKeys)=>{\n                const newKeys = [\n                    optimisticKey,\n                    ...prevKeys\n                ];\n                // Immediately update subscription info with new count\n                fetchSubscriptionInfo(newKeys.length);\n                return newKeys;\n            });\n            // Also refresh to ensure we have the latest data and correct counts\n            await fetchApiKeys();\n            return newApiKey;\n        } catch (error) {\n            console.error('Error creating API key:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(error.message || 'Failed to create API key');\n            throw error;\n        } finally{\n            setCreating(false);\n        }\n    };\n    const handleEditApiKey = async (keyId, updates)=>{\n        try {\n            // Optimistically update the key in the list\n            const previousKeys = [\n                ...apiKeys\n            ];\n            setApiKeys((prevKeys)=>prevKeys.map((key)=>key.id === keyId ? {\n                        ...key,\n                        ...updates\n                    } : key));\n            const response = await fetch(\"/api/user-api-keys/\".concat(keyId), {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(updates)\n            });\n            if (!response.ok) {\n                // Revert the optimistic update on error\n                setApiKeys(previousKeys);\n                const error = await response.json();\n                throw new Error(error.error || 'Failed to update API key');\n            }\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success('API key updated successfully');\n            // Refresh to ensure we have the latest data\n            await fetchApiKeys();\n            setShowEditDialog(false);\n            setSelectedApiKey(null);\n        } catch (error) {\n            console.error('Error updating API key:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(error.message || 'Failed to update API key');\n        }\n    };\n    const handleRevokeApiKey = async (keyId)=>{\n        if (!confirm('Are you sure you want to revoke this API key? This action cannot be undone.')) {\n            return;\n        }\n        try {\n            // Optimistically remove the key from the list\n            const previousKeys = [\n                ...apiKeys\n            ];\n            setApiKeys((prevKeys)=>{\n                const newKeys = prevKeys.filter((key)=>key.id !== keyId);\n                // Immediately update subscription info with new count\n                fetchSubscriptionInfo(newKeys.length);\n                return newKeys;\n            });\n            const response = await fetch(\"/api/user-api-keys/\".concat(keyId), {\n                method: 'DELETE'\n            });\n            if (!response.ok) {\n                // Revert the optimistic update on error\n                setApiKeys(previousKeys);\n                const error = await response.json();\n                throw new Error(error.error || 'Failed to revoke API key');\n            }\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success('API key revoked successfully');\n            // Refresh to ensure correct counts and latest data\n            await fetchApiKeys();\n        } catch (error) {\n            console.error('Error revoking API key:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(error.message || 'Failed to revoke API key');\n        }\n    };\n    const handleViewUsage = (keyId)=>{\n        const apiKey = apiKeys.find((key)=>key.id === keyId);\n        setSelectedApiKey(apiKey);\n        setShowUsageDialog(true);\n    };\n    const handleCreateDialogClose = (open)=>{\n        setShowCreateDialog(open);\n    // Note: We no longer need to refresh here since we refresh immediately after creation\n    };\n    const canCreateMoreKeys = subscriptionInfo ? subscriptionInfo.currentCount < subscriptionInfo.keyLimit : true;\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"flex items-center justify-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-6 w-6 animate-spin mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, this),\n                    \"Loading API keys...\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 244,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n            lineNumber: 243,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"API Keys\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: [\n                                    \"Generate API keys for programmatic access to \",\n                                    configName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 9\n                    }, this),\n                    canCreateMoreKeys ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: ()=>setShowCreateDialog(true),\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 13\n                            }, this),\n                            \"Create API Key\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-end gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                disabled: true,\n                                className: \"flex items-center gap-2 opacity-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Create API Key\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-orange-600 font-medium\",\n                                children: (subscriptionInfo === null || subscriptionInfo === void 0 ? void 0 : subscriptionInfo.tier) === 'free' ? 'Upgrade to Starter plan for more API keys' : 'API key limit reached - upgrade for unlimited keys'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 255,\n                columnNumber: 7\n            }, this),\n            subscriptionInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg border border-gray-200 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_9__.LimitIndicator, {\n                    current: subscriptionInfo.currentCount,\n                    limit: subscriptionInfo.keyLimit,\n                    label: \"User-Generated API Keys\",\n                    tier: subscriptionInfo.tier,\n                    showUpgradeHint: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 294,\n                columnNumber: 9\n            }, this),\n            apiKeys.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"text-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            className: \"h-12 w-12 mx-auto text-gray-400 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-2\",\n                            children: \"No API Keys\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Create your first API key to start using the RouKey API programmatically.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 13\n                        }, this),\n                        canCreateMoreKeys ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: ()=>setShowCreateDialog(true),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 17\n                                }, this),\n                                \"Create Your First API Key\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    disabled: true,\n                                    className: \"opacity-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Create Your First API Key\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-orange-600 font-medium\",\n                                    children: (subscriptionInfo === null || subscriptionInfo === void 0 ? void 0 : subscriptionInfo.tier) === 'free' ? 'Upgrade to Starter plan to create API keys' : 'API key limit reached - upgrade for unlimited keys'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                    lineNumber: 308,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 307,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4\",\n                children: apiKeys.map((apiKey)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ApiKeyCard__WEBPACK_IMPORTED_MODULE_5__.ApiKeyCard, {\n                        apiKey: apiKey,\n                        onEdit: (key)=>{\n                            setSelectedApiKey(key);\n                            setShowEditDialog(true);\n                        },\n                        onRevoke: handleRevokeApiKey,\n                        onViewUsage: handleViewUsage\n                    }, apiKey.id, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 338,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateApiKeyDialog__WEBPACK_IMPORTED_MODULE_6__.CreateApiKeyDialog, {\n                open: showCreateDialog,\n                onOpenChange: handleCreateDialogClose,\n                onCreateApiKey: handleCreateApiKey,\n                configName: configName,\n                creating: creating,\n                subscriptionTier: (subscriptionInfo === null || subscriptionInfo === void 0 ? void 0 : subscriptionInfo.tier) || 'starter'\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 355,\n                columnNumber: 7\n            }, this),\n            selectedApiKey && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EditApiKeyDialog__WEBPACK_IMPORTED_MODULE_7__.EditApiKeyDialog, {\n                        open: showEditDialog,\n                        onOpenChange: setShowEditDialog,\n                        apiKey: selectedApiKey,\n                        onUpdateApiKey: (updates)=>handleEditApiKey(selectedApiKey.id, updates)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ApiKeyUsageDialog__WEBPACK_IMPORTED_MODULE_8__.ApiKeyUsageDialog, {\n                        open: showUsageDialog,\n                        onOpenChange: setShowUsageDialog,\n                        apiKey: selectedApiKey\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 373,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n        lineNumber: 253,\n        columnNumber: 5\n    }, this);\n}\n_s(ApiKeyManager, \"FbaUjxeLQ7jhWAVpAmW1g8u12LY=\");\n_c = ApiKeyManager;\nvar _c;\n$RefreshReg$(_c, \"ApiKeyManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1VzZXJBcGlLZXlzL0FwaUtleU1hbmFnZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRW1EO0FBQ0g7QUFDZ0M7QUFFZjtBQUNsQztBQUNXO0FBQ2dCO0FBQ0o7QUFDRTtBQUVNO0FBU3ZELFNBQVNlLGNBQWMsS0FBNEM7UUFBNUMsRUFBRUMsUUFBUSxFQUFFQyxVQUFVLEVBQXNCLEdBQTVDOztJQUM1QixNQUFNLENBQUNDLFNBQVNDLFdBQVcsR0FBR2xCLCtDQUFRQSxDQUFRLEVBQUU7SUFDaEQsTUFBTSxDQUFDbUIsU0FBU0MsV0FBVyxHQUFHcEIsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDcUIsVUFBVUMsWUFBWSxHQUFHdEIsK0NBQVFBLENBQUM7SUFDekMsTUFBTSxDQUFDdUIsa0JBQWtCQyxvQkFBb0IsR0FBR3hCLCtDQUFRQSxDQUFDO0lBQ3pELE1BQU0sQ0FBQ3lCLGdCQUFnQkMsa0JBQWtCLEdBQUcxQiwrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUMyQixpQkFBaUJDLG1CQUFtQixHQUFHNUIsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDNkIsZ0JBQWdCQyxrQkFBa0IsR0FBRzlCLCtDQUFRQSxDQUFNO0lBQzFELE1BQU0sQ0FBQytCLGtCQUFrQkMsb0JBQW9CLEdBQUdoQywrQ0FBUUEsQ0FJOUM7SUFFVix3Q0FBd0M7SUFDeEMsTUFBTWlDLGVBQWU7UUFDbkIsSUFBSTtZQUNGYixXQUFXO1lBQ1gsTUFBTWMsV0FBVyxNQUFNQyxNQUFNLGdDQUF5QyxPQUFUcEI7WUFFN0QsSUFBSSxDQUFDbUIsU0FBU0UsRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUlDLE1BQU07WUFDbEI7WUFFQSxNQUFNQyxPQUFPLE1BQU1KLFNBQVNLLElBQUk7WUFDaENyQixXQUFXb0IsS0FBS0UsUUFBUSxJQUFJLEVBQUU7UUFDaEMsRUFBRSxPQUFPQyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw0QkFBNEJBO1lBQzFDakMseUNBQUtBLENBQUNpQyxLQUFLLENBQUM7UUFDZCxTQUFVO1lBQ1JyQixXQUFXO1FBQ2I7SUFDRjtJQUVBLDBCQUEwQjtJQUMxQixNQUFNdUIsd0JBQXdCLE9BQU9DO1FBQ25DLElBQUk7WUFDRixtREFBbUQ7WUFDbkQsTUFBTUMsZUFBZSxNQUFNVixNQUFNO1lBQ2pDLE1BQU1XLFdBQVdELGFBQWFULEVBQUUsR0FBRyxNQUFNUyxhQUFhTixJQUFJLEtBQUs7WUFDL0QsTUFBTVEsT0FBT0QsQ0FBQUEscUJBQUFBLCtCQUFBQSxTQUFVQyxJQUFJLEtBQUk7WUFFL0IsTUFBTUMsU0FBUztnQkFDYkMsTUFBTTtnQkFDTkMsU0FBUztnQkFDVEMsY0FBYztnQkFDZEMsWUFBWTtZQUNkO1lBRUEsK0NBQStDO1lBQy9DLE1BQU1DLFdBQVdULG9CQUFvQlUsWUFBWVYsa0JBQWtCM0IsUUFBUXNDLE1BQU07WUFFakZ2QixvQkFBb0I7Z0JBQ2xCZTtnQkFDQVMsVUFBVVIsTUFBTSxDQUFDRCxLQUE0QixJQUFJQyxPQUFPQyxJQUFJO2dCQUM1RFEsY0FBY0o7WUFDaEI7UUFDRixFQUFFLE9BQU9aLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLHFDQUFxQ0E7WUFDbkQsd0JBQXdCO1lBQ3hCLE1BQU1ZLFdBQVdULG9CQUFvQlUsWUFBWVYsa0JBQWtCM0IsUUFBUXNDLE1BQU07WUFDakZ2QixvQkFBb0I7Z0JBQ2xCZSxNQUFNO2dCQUNOUyxVQUFVO2dCQUNWQyxjQUFjSjtZQUNoQjtRQUNGO0lBQ0Y7SUFFQXBELGdEQUFTQTttQ0FBQztZQUNSZ0M7UUFDRjtrQ0FBRztRQUFDbEI7S0FBUztJQUViZCxnREFBU0E7bUNBQUM7WUFDUixJQUFJZ0IsUUFBUXNDLE1BQU0sSUFBSSxHQUFHO2dCQUN2Qlo7WUFDRjtRQUNGO2tDQUFHO1FBQUMxQjtLQUFRO0lBRVosTUFBTXlDLHFCQUFxQixPQUFPQztRQUNoQyxJQUFJO1lBQ0ZyQyxZQUFZO1lBQ1osTUFBTVksV0FBVyxNQUFNQyxNQUFNLHNCQUFzQjtnQkFDakR5QixRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFDbkIsR0FBR0wsT0FBTztvQkFDVk0sc0JBQXNCbEQ7Z0JBQ3hCO1lBQ0Y7WUFFQSxJQUFJLENBQUNtQixTQUFTRSxFQUFFLEVBQUU7Z0JBQ2hCLE1BQU1LLFFBQVEsTUFBTVAsU0FBU0ssSUFBSTtnQkFDakMsTUFBTSxJQUFJRixNQUFNSSxNQUFNQSxLQUFLLElBQUk7WUFDakM7WUFFQSxNQUFNeUIsWUFBWSxNQUFNaEMsU0FBU0ssSUFBSTtZQUVyQyx1RUFBdUU7WUFDdkUvQix5Q0FBS0EsQ0FBQzJELE9BQU8sQ0FBQztZQUVkLHlEQUF5RDtZQUN6RCxNQUFNQyxnQkFBZ0I7Z0JBQ3BCLEdBQUdGLFNBQVM7Z0JBQ1osMERBQTBEO2dCQUMxREcsb0JBQW9CO29CQUNsQkMsSUFBSXZEO29CQUNKd0QsTUFBTXZEO2dCQUNSO1lBQ0Y7WUFDQUUsV0FBV3NELENBQUFBO2dCQUNULE1BQU1DLFVBQVU7b0JBQUNMO3VCQUFrQkk7aUJBQVM7Z0JBQzVDLHNEQUFzRDtnQkFDdEQ3QixzQkFBc0I4QixRQUFRbEIsTUFBTTtnQkFDcEMsT0FBT2tCO1lBQ1Q7WUFFQSxvRUFBb0U7WUFDcEUsTUFBTXhDO1lBRU4sT0FBT2lDO1FBQ1QsRUFBRSxPQUFPekIsT0FBWTtZQUNuQkMsUUFBUUQsS0FBSyxDQUFDLDJCQUEyQkE7WUFDekNqQyx5Q0FBS0EsQ0FBQ2lDLEtBQUssQ0FBQ0EsTUFBTWlDLE9BQU8sSUFBSTtZQUM3QixNQUFNakM7UUFDUixTQUFVO1lBQ1JuQixZQUFZO1FBQ2Q7SUFDRjtJQUVBLE1BQU1xRCxtQkFBbUIsT0FBT0MsT0FBZUM7UUFDN0MsSUFBSTtZQUNGLDRDQUE0QztZQUM1QyxNQUFNQyxlQUFlO21CQUFJN0Q7YUFBUTtZQUNqQ0MsV0FBV3NELENBQUFBLFdBQ1RBLFNBQVNPLEdBQUcsQ0FBQ0MsQ0FBQUEsTUFDWEEsSUFBSVYsRUFBRSxLQUFLTSxRQUFRO3dCQUFFLEdBQUdJLEdBQUc7d0JBQUUsR0FBR0gsT0FBTztvQkFBQyxJQUFJRztZQUloRCxNQUFNOUMsV0FBVyxNQUFNQyxNQUFNLHNCQUE0QixPQUFOeUMsUUFBUztnQkFDMURoQixRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ2E7WUFDdkI7WUFFQSxJQUFJLENBQUMzQyxTQUFTRSxFQUFFLEVBQUU7Z0JBQ2hCLHdDQUF3QztnQkFDeENsQixXQUFXNEQ7Z0JBQ1gsTUFBTXJDLFFBQVEsTUFBTVAsU0FBU0ssSUFBSTtnQkFDakMsTUFBTSxJQUFJRixNQUFNSSxNQUFNQSxLQUFLLElBQUk7WUFDakM7WUFFQWpDLHlDQUFLQSxDQUFDMkQsT0FBTyxDQUFDO1lBQ2QsNENBQTRDO1lBQzVDLE1BQU1sQztZQUNOUCxrQkFBa0I7WUFDbEJJLGtCQUFrQjtRQUNwQixFQUFFLE9BQU9XLE9BQVk7WUFDbkJDLFFBQVFELEtBQUssQ0FBQywyQkFBMkJBO1lBQ3pDakMseUNBQUtBLENBQUNpQyxLQUFLLENBQUNBLE1BQU1pQyxPQUFPLElBQUk7UUFDL0I7SUFDRjtJQUVBLE1BQU1PLHFCQUFxQixPQUFPTDtRQUNoQyxJQUFJLENBQUNNLFFBQVEsZ0ZBQWdGO1lBQzNGO1FBQ0Y7UUFFQSxJQUFJO1lBQ0YsOENBQThDO1lBQzlDLE1BQU1KLGVBQWU7bUJBQUk3RDthQUFRO1lBQ2pDQyxXQUFXc0QsQ0FBQUE7Z0JBQ1QsTUFBTUMsVUFBVUQsU0FBU1csTUFBTSxDQUFDSCxDQUFBQSxNQUFPQSxJQUFJVixFQUFFLEtBQUtNO2dCQUNsRCxzREFBc0Q7Z0JBQ3REakMsc0JBQXNCOEIsUUFBUWxCLE1BQU07Z0JBQ3BDLE9BQU9rQjtZQUNUO1lBRUEsTUFBTXZDLFdBQVcsTUFBTUMsTUFBTSxzQkFBNEIsT0FBTnlDLFFBQVM7Z0JBQzFEaEIsUUFBUTtZQUNWO1lBRUEsSUFBSSxDQUFDMUIsU0FBU0UsRUFBRSxFQUFFO2dCQUNoQix3Q0FBd0M7Z0JBQ3hDbEIsV0FBVzREO2dCQUNYLE1BQU1yQyxRQUFRLE1BQU1QLFNBQVNLLElBQUk7Z0JBQ2pDLE1BQU0sSUFBSUYsTUFBTUksTUFBTUEsS0FBSyxJQUFJO1lBQ2pDO1lBRUFqQyx5Q0FBS0EsQ0FBQzJELE9BQU8sQ0FBQztZQUNkLG1EQUFtRDtZQUNuRCxNQUFNbEM7UUFDUixFQUFFLE9BQU9RLE9BQVk7WUFDbkJDLFFBQVFELEtBQUssQ0FBQywyQkFBMkJBO1lBQ3pDakMseUNBQUtBLENBQUNpQyxLQUFLLENBQUNBLE1BQU1pQyxPQUFPLElBQUk7UUFDL0I7SUFDRjtJQUVBLE1BQU1VLGtCQUFrQixDQUFDUjtRQUN2QixNQUFNUyxTQUFTcEUsUUFBUXFFLElBQUksQ0FBQ04sQ0FBQUEsTUFBT0EsSUFBSVYsRUFBRSxLQUFLTTtRQUM5QzlDLGtCQUFrQnVEO1FBQ2xCekQsbUJBQW1CO0lBQ3JCO0lBRUEsTUFBTTJELDBCQUEwQixDQUFDQztRQUMvQmhFLG9CQUFvQmdFO0lBQ3BCLHNGQUFzRjtJQUN4RjtJQUVBLE1BQU1DLG9CQUFvQjFELG1CQUN0QkEsaUJBQWlCMEIsWUFBWSxHQUFHMUIsaUJBQWlCeUIsUUFBUSxHQUN6RDtJQUVKLElBQUlyQyxTQUFTO1FBQ1gscUJBQ0UsOERBQUNoQixxREFBSUE7c0JBQ0gsNEVBQUNDLDREQUFXQTtnQkFBQ3NGLFdBQVU7O2tDQUNyQiw4REFBQ25GLCtGQUFTQTt3QkFBQ21GLFdBQVU7Ozs7OztvQkFBOEI7Ozs7Ozs7Ozs7OztJQUszRDtJQUVBLHFCQUNFLDhEQUFDQztRQUFJRCxXQUFVOzswQkFFYiw4REFBQ0M7Z0JBQUlELFdBQVU7O2tDQUNiLDhEQUFDQzs7MENBQ0MsOERBQUNDO2dDQUFHRixXQUFVOztrREFDWiw4REFBQ3BGLCtGQUFHQTt3Q0FBQ29GLFdBQVU7Ozs7OztvQ0FBWTs7Ozs7OzswQ0FHN0IsOERBQUNHO2dDQUFFSCxXQUFVOztvQ0FBcUI7b0NBQ2MxRTs7Ozs7Ozs7Ozs7OztvQkFHakR5RSxrQ0FDQyw4REFBQ3ZGLHlEQUFNQTt3QkFDTDRGLFNBQVMsSUFBTXRFLG9CQUFvQjt3QkFDbkNrRSxXQUFVOzswQ0FFViw4REFBQ3JGLCtGQUFJQTtnQ0FBQ3FGLFdBQVU7Ozs7Ozs0QkFBWTs7Ozs7OzZDQUk5Qiw4REFBQ0M7d0JBQUlELFdBQVU7OzBDQUNiLDhEQUFDeEYseURBQU1BO2dDQUNMNkYsUUFBUTtnQ0FDUkwsV0FBVTs7a0RBRVYsOERBQUNyRiwrRkFBSUE7d0NBQUNxRixXQUFVOzs7Ozs7b0NBQVk7Ozs7Ozs7MENBRzlCLDhEQUFDRztnQ0FBRUgsV0FBVTswQ0FDVjNELENBQUFBLDZCQUFBQSx1Q0FBQUEsaUJBQWtCZ0IsSUFBSSxNQUFLLFNBQ3hCLDhDQUNBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFRWGhCLGtDQUNDLDhEQUFDNEQ7Z0JBQUlELFdBQVU7MEJBQ2IsNEVBQUM3RSx1RUFBY0E7b0JBQ2JtRixTQUFTakUsaUJBQWlCMEIsWUFBWTtvQkFDdEN3QyxPQUFPbEUsaUJBQWlCeUIsUUFBUTtvQkFDaEMwQyxPQUFNO29CQUNObkQsTUFBTWhCLGlCQUFpQmdCLElBQUk7b0JBQzNCb0QsaUJBQWlCOzs7Ozs7Ozs7OztZQU10QmxGLFFBQVFzQyxNQUFNLEtBQUssa0JBQ2xCLDhEQUFDcEQscURBQUlBOzBCQUNILDRFQUFDQyw0REFBV0E7b0JBQUNzRixXQUFVOztzQ0FDckIsOERBQUNwRiwrRkFBR0E7NEJBQUNvRixXQUFVOzs7Ozs7c0NBQ2YsOERBQUNVOzRCQUFHVixXQUFVO3NDQUE2Qjs7Ozs7O3NDQUMzQyw4REFBQ0c7NEJBQUVILFdBQVU7c0NBQXFCOzs7Ozs7d0JBR2pDRCxrQ0FDQyw4REFBQ3ZGLHlEQUFNQTs0QkFDTDRGLFNBQVMsSUFBTXRFLG9CQUFvQjs7OENBRW5DLDhEQUFDbkIsK0ZBQUlBO29DQUFDcUYsV0FBVTs7Ozs7O2dDQUFpQjs7Ozs7O2lEQUluQyw4REFBQ0M7NEJBQUlELFdBQVU7OzhDQUNiLDhEQUFDeEYseURBQU1BO29DQUFDNkYsUUFBUTtvQ0FBQ0wsV0FBVTs7c0RBQ3pCLDhEQUFDckYsK0ZBQUlBOzRDQUFDcUYsV0FBVTs7Ozs7O3dDQUFpQjs7Ozs7Ozs4Q0FHbkMsOERBQUNHO29DQUFFSCxXQUFVOzhDQUNWM0QsQ0FBQUEsNkJBQUFBLHVDQUFBQSxpQkFBa0JnQixJQUFJLE1BQUssU0FDeEIsK0NBQ0E7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7cUNBUWQsOERBQUM0QztnQkFBSUQsV0FBVTswQkFDWnpFLFFBQVE4RCxHQUFHLENBQUMsQ0FBQ00sdUJBQ1osOERBQUM1RSxtREFBVUE7d0JBRVQ0RSxRQUFRQTt3QkFDUmdCLFFBQVEsQ0FBQ3JCOzRCQUNQbEQsa0JBQWtCa0Q7NEJBQ2xCdEQsa0JBQWtCO3dCQUNwQjt3QkFDQTRFLFVBQVVyQjt3QkFDVnNCLGFBQWFuQjt1QkFQUkMsT0FBT2YsRUFBRTs7Ozs7Ozs7OzswQkFjdEIsOERBQUM1RCxtRUFBa0JBO2dCQUNqQjhFLE1BQU1qRTtnQkFDTmlGLGNBQWNqQjtnQkFDZGtCLGdCQUFnQi9DO2dCQUNoQjFDLFlBQVlBO2dCQUNaSyxVQUFVQTtnQkFDVnFGLGtCQUFrQjNFLENBQUFBLDZCQUFBQSx1Q0FBQUEsaUJBQWtCZ0IsSUFBSSxLQUFJOzs7Ozs7WUFHN0NsQixnQ0FDQzs7a0NBQ0UsOERBQUNsQiwrREFBZ0JBO3dCQUNmNkUsTUFBTS9EO3dCQUNOK0UsY0FBYzlFO3dCQUNkMkQsUUFBUXhEO3dCQUNSOEUsZ0JBQWdCLENBQUM5QixVQUFZRixpQkFBaUI5QyxlQUFleUMsRUFBRSxFQUFFTzs7Ozs7O2tDQUduRSw4REFBQ2pFLGlFQUFpQkE7d0JBQ2hCNEUsTUFBTTdEO3dCQUNONkUsY0FBYzVFO3dCQUNkeUQsUUFBUXhEOzs7Ozs7Ozs7Ozs7OztBQU1wQjtHQXZXZ0JmO0tBQUFBIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcY29tcG9uZW50c1xcVXNlckFwaUtleXNcXEFwaUtleU1hbmFnZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvQnV0dG9uJztcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvQ2FyZCc7XG5pbXBvcnQgeyBCYWRnZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9iYWRnZSc7XG5pbXBvcnQgeyBQbHVzLCBLZXksIEFsZXJ0Q2lyY2xlLCBSZWZyZXNoQ3cgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IHsgdG9hc3QgfSBmcm9tICdzb25uZXInO1xuaW1wb3J0IHsgQXBpS2V5Q2FyZCB9IGZyb20gJy4vQXBpS2V5Q2FyZCc7XG5pbXBvcnQgeyBDcmVhdGVBcGlLZXlEaWFsb2cgfSBmcm9tICcuL0NyZWF0ZUFwaUtleURpYWxvZyc7XG5pbXBvcnQgeyBFZGl0QXBpS2V5RGlhbG9nIH0gZnJvbSAnLi9FZGl0QXBpS2V5RGlhbG9nJztcbmltcG9ydCB7IEFwaUtleVVzYWdlRGlhbG9nIH0gZnJvbSAnLi9BcGlLZXlVc2FnZURpYWxvZyc7XG5pbXBvcnQgeyBBbGVydCwgQWxlcnREZXNjcmlwdGlvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9hbGVydCc7XG5pbXBvcnQgeyBMaW1pdEluZGljYXRvciB9IGZyb20gJ0AvY29tcG9uZW50cy9UaWVyRW5mb3JjZW1lbnQnO1xuaW1wb3J0IENvbmZpcm1hdGlvbk1vZGFsIGZyb20gJ0AvY29tcG9uZW50cy91aS9Db25maXJtYXRpb25Nb2RhbCc7XG5pbXBvcnQgeyB1c2VDb25maXJtYXRpb24gfSBmcm9tICdAL2hvb2tzL3VzZUNvbmZpcm1hdGlvbic7XG5cbmludGVyZmFjZSBBcGlLZXlNYW5hZ2VyUHJvcHMge1xuICBjb25maWdJZDogc3RyaW5nO1xuICBjb25maWdOYW1lOiBzdHJpbmc7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBBcGlLZXlNYW5hZ2VyKHsgY29uZmlnSWQsIGNvbmZpZ05hbWUgfTogQXBpS2V5TWFuYWdlclByb3BzKSB7XG4gIGNvbnN0IFthcGlLZXlzLCBzZXRBcGlLZXlzXSA9IHVzZVN0YXRlPGFueVtdPihbXSk7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBbY3JlYXRpbmcsIHNldENyZWF0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3Nob3dDcmVhdGVEaWFsb2csIHNldFNob3dDcmVhdGVEaWFsb2ddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc2hvd0VkaXREaWFsb2csIHNldFNob3dFZGl0RGlhbG9nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3Nob3dVc2FnZURpYWxvZywgc2V0U2hvd1VzYWdlRGlhbG9nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3NlbGVjdGVkQXBpS2V5LCBzZXRTZWxlY3RlZEFwaUtleV0gPSB1c2VTdGF0ZTxhbnk+KG51bGwpO1xuICBjb25zdCBbc3Vic2NyaXB0aW9uSW5mbywgc2V0U3Vic2NyaXB0aW9uSW5mb10gPSB1c2VTdGF0ZTx7XG4gICAgdGllcjogc3RyaW5nO1xuICAgIGtleUxpbWl0OiBudW1iZXI7XG4gICAgY3VycmVudENvdW50OiBudW1iZXI7XG4gIH0gfCBudWxsPihudWxsKTtcblxuICAvLyBGZXRjaCBBUEkga2V5cyBmb3IgdGhpcyBjb25maWd1cmF0aW9uXG4gIGNvbnN0IGZldGNoQXBpS2V5cyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvdXNlci1hcGkta2V5cz9jb25maWdfaWQ9JHtjb25maWdJZH1gKTtcbiAgICAgIFxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byBmZXRjaCBBUEkga2V5cycpO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgc2V0QXBpS2V5cyhkYXRhLmFwaV9rZXlzIHx8IFtdKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgQVBJIGtleXM6JywgZXJyb3IpO1xuICAgICAgdG9hc3QuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIEFQSSBrZXlzJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyBGZXRjaCBzdWJzY3JpcHRpb24gaW5mb1xuICBjb25zdCBmZXRjaFN1YnNjcmlwdGlvbkluZm8gPSBhc3luYyAoY3VycmVudEtleUNvdW50PzogbnVtYmVyKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIEdldCB0aGUgdGllciBmcm9tIHRoZSB1c2VyJ3MgYWN0aXZlIHN1YnNjcmlwdGlvblxuICAgICAgY29uc3QgdGllclJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvdXNlci9zdWJzY3JpcHRpb24tdGllcicpO1xuICAgICAgY29uc3QgdGllckRhdGEgPSB0aWVyUmVzcG9uc2Uub2sgPyBhd2FpdCB0aWVyUmVzcG9uc2UuanNvbigpIDogbnVsbDtcbiAgICAgIGNvbnN0IHRpZXIgPSB0aWVyRGF0YT8udGllciB8fCAnc3RhcnRlcic7XG5cbiAgICAgIGNvbnN0IGxpbWl0cyA9IHtcbiAgICAgICAgZnJlZTogMyxcbiAgICAgICAgc3RhcnRlcjogNTAsXG4gICAgICAgIHByb2Zlc3Npb25hbDogOTk5OTk5LFxuICAgICAgICBlbnRlcnByaXNlOiA5OTk5OTlcbiAgICAgIH07XG5cbiAgICAgIC8vIFVzZSBwcm92aWRlZCBjb3VudCBvciBjdXJyZW50IGFwaUtleXMgbGVuZ3RoXG4gICAgICBjb25zdCBrZXlDb3VudCA9IGN1cnJlbnRLZXlDb3VudCAhPT0gdW5kZWZpbmVkID8gY3VycmVudEtleUNvdW50IDogYXBpS2V5cy5sZW5ndGg7XG5cbiAgICAgIHNldFN1YnNjcmlwdGlvbkluZm8oe1xuICAgICAgICB0aWVyLFxuICAgICAgICBrZXlMaW1pdDogbGltaXRzW3RpZXIgYXMga2V5b2YgdHlwZW9mIGxpbWl0c10gfHwgbGltaXRzLmZyZWUsXG4gICAgICAgIGN1cnJlbnRDb3VudDoga2V5Q291bnRcbiAgICAgIH0pO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBzdWJzY3JpcHRpb24gaW5mbzonLCBlcnJvcik7XG4gICAgICAvLyBGYWxsYmFjayB0byBmcmVlIHRpZXJcbiAgICAgIGNvbnN0IGtleUNvdW50ID0gY3VycmVudEtleUNvdW50ICE9PSB1bmRlZmluZWQgPyBjdXJyZW50S2V5Q291bnQgOiBhcGlLZXlzLmxlbmd0aDtcbiAgICAgIHNldFN1YnNjcmlwdGlvbkluZm8oe1xuICAgICAgICB0aWVyOiAnZnJlZScsXG4gICAgICAgIGtleUxpbWl0OiAzLFxuICAgICAgICBjdXJyZW50Q291bnQ6IGtleUNvdW50XG4gICAgICB9KTtcbiAgICB9XG4gIH07XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBmZXRjaEFwaUtleXMoKTtcbiAgfSwgW2NvbmZpZ0lkXSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoYXBpS2V5cy5sZW5ndGggPj0gMCkge1xuICAgICAgZmV0Y2hTdWJzY3JpcHRpb25JbmZvKCk7XG4gICAgfVxuICB9LCBbYXBpS2V5c10pO1xuXG4gIGNvbnN0IGhhbmRsZUNyZWF0ZUFwaUtleSA9IGFzeW5jIChrZXlEYXRhOiBhbnkpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0Q3JlYXRpbmcodHJ1ZSk7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL3VzZXItYXBpLWtleXMnLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgIC4uLmtleURhdGEsXG4gICAgICAgICAgY3VzdG9tX2FwaV9jb25maWdfaWQ6IGNvbmZpZ0lkLFxuICAgICAgICB9KSxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IGVycm9yID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3IuZXJyb3IgfHwgJ0ZhaWxlZCB0byBjcmVhdGUgQVBJIGtleScpO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBuZXdBcGlLZXkgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG5cbiAgICAgIC8vIFNob3cgdGhlIGZ1bGwgQVBJIGtleSBpbiBhIHNwZWNpYWwgZGlhbG9nIHNpbmNlIGl0J3Mgb25seSBzaG93biBvbmNlXG4gICAgICB0b2FzdC5zdWNjZXNzKCdBUEkga2V5IGNyZWF0ZWQgc3VjY2Vzc2Z1bGx5IScpO1xuXG4gICAgICAvLyBPcHRpbWlzdGljYWxseSBhZGQgdGhlIG5ldyBrZXkgdG8gdGhlIGxpc3QgaW1tZWRpYXRlbHlcbiAgICAgIGNvbnN0IG9wdGltaXN0aWNLZXkgPSB7XG4gICAgICAgIC4uLm5ld0FwaUtleSxcbiAgICAgICAgLy8gQWRkIGFueSBtaXNzaW5nIGZpZWxkcyB0aGF0IG1pZ2h0IGJlIG5lZWRlZCBmb3IgZGlzcGxheVxuICAgICAgICBjdXN0b21fYXBpX2NvbmZpZ3M6IHtcbiAgICAgICAgICBpZDogY29uZmlnSWQsXG4gICAgICAgICAgbmFtZTogY29uZmlnTmFtZVxuICAgICAgICB9XG4gICAgICB9O1xuICAgICAgc2V0QXBpS2V5cyhwcmV2S2V5cyA9PiB7XG4gICAgICAgIGNvbnN0IG5ld0tleXMgPSBbb3B0aW1pc3RpY0tleSwgLi4ucHJldktleXNdO1xuICAgICAgICAvLyBJbW1lZGlhdGVseSB1cGRhdGUgc3Vic2NyaXB0aW9uIGluZm8gd2l0aCBuZXcgY291bnRcbiAgICAgICAgZmV0Y2hTdWJzY3JpcHRpb25JbmZvKG5ld0tleXMubGVuZ3RoKTtcbiAgICAgICAgcmV0dXJuIG5ld0tleXM7XG4gICAgICB9KTtcblxuICAgICAgLy8gQWxzbyByZWZyZXNoIHRvIGVuc3VyZSB3ZSBoYXZlIHRoZSBsYXRlc3QgZGF0YSBhbmQgY29ycmVjdCBjb3VudHNcbiAgICAgIGF3YWl0IGZldGNoQXBpS2V5cygpO1xuXG4gICAgICByZXR1cm4gbmV3QXBpS2V5O1xuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNyZWF0aW5nIEFQSSBrZXk6JywgZXJyb3IpO1xuICAgICAgdG9hc3QuZXJyb3IoZXJyb3IubWVzc2FnZSB8fCAnRmFpbGVkIHRvIGNyZWF0ZSBBUEkga2V5Jyk7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0Q3JlYXRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVFZGl0QXBpS2V5ID0gYXN5bmMgKGtleUlkOiBzdHJpbmcsIHVwZGF0ZXM6IGFueSkgPT4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBPcHRpbWlzdGljYWxseSB1cGRhdGUgdGhlIGtleSBpbiB0aGUgbGlzdFxuICAgICAgY29uc3QgcHJldmlvdXNLZXlzID0gWy4uLmFwaUtleXNdO1xuICAgICAgc2V0QXBpS2V5cyhwcmV2S2V5cyA9PlxuICAgICAgICBwcmV2S2V5cy5tYXAoa2V5ID0+XG4gICAgICAgICAga2V5LmlkID09PSBrZXlJZCA/IHsgLi4ua2V5LCAuLi51cGRhdGVzIH0gOiBrZXlcbiAgICAgICAgKVxuICAgICAgKTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS91c2VyLWFwaS1rZXlzLyR7a2V5SWR9YCwge1xuICAgICAgICBtZXRob2Q6ICdQQVRDSCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh1cGRhdGVzKSxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIC8vIFJldmVydCB0aGUgb3B0aW1pc3RpYyB1cGRhdGUgb24gZXJyb3JcbiAgICAgICAgc2V0QXBpS2V5cyhwcmV2aW91c0tleXMpO1xuICAgICAgICBjb25zdCBlcnJvciA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yLmVycm9yIHx8ICdGYWlsZWQgdG8gdXBkYXRlIEFQSSBrZXknKTtcbiAgICAgIH1cblxuICAgICAgdG9hc3Quc3VjY2VzcygnQVBJIGtleSB1cGRhdGVkIHN1Y2Nlc3NmdWxseScpO1xuICAgICAgLy8gUmVmcmVzaCB0byBlbnN1cmUgd2UgaGF2ZSB0aGUgbGF0ZXN0IGRhdGFcbiAgICAgIGF3YWl0IGZldGNoQXBpS2V5cygpO1xuICAgICAgc2V0U2hvd0VkaXREaWFsb2coZmFsc2UpO1xuICAgICAgc2V0U2VsZWN0ZWRBcGlLZXkobnVsbCk7XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgdXBkYXRpbmcgQVBJIGtleTonLCBlcnJvcik7XG4gICAgICB0b2FzdC5lcnJvcihlcnJvci5tZXNzYWdlIHx8ICdGYWlsZWQgdG8gdXBkYXRlIEFQSSBrZXknKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlUmV2b2tlQXBpS2V5ID0gYXN5bmMgKGtleUlkOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIWNvbmZpcm0oJ0FyZSB5b3Ugc3VyZSB5b3Ugd2FudCB0byByZXZva2UgdGhpcyBBUEkga2V5PyBUaGlzIGFjdGlvbiBjYW5ub3QgYmUgdW5kb25lLicpKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIC8vIE9wdGltaXN0aWNhbGx5IHJlbW92ZSB0aGUga2V5IGZyb20gdGhlIGxpc3RcbiAgICAgIGNvbnN0IHByZXZpb3VzS2V5cyA9IFsuLi5hcGlLZXlzXTtcbiAgICAgIHNldEFwaUtleXMocHJldktleXMgPT4ge1xuICAgICAgICBjb25zdCBuZXdLZXlzID0gcHJldktleXMuZmlsdGVyKGtleSA9PiBrZXkuaWQgIT09IGtleUlkKTtcbiAgICAgICAgLy8gSW1tZWRpYXRlbHkgdXBkYXRlIHN1YnNjcmlwdGlvbiBpbmZvIHdpdGggbmV3IGNvdW50XG4gICAgICAgIGZldGNoU3Vic2NyaXB0aW9uSW5mbyhuZXdLZXlzLmxlbmd0aCk7XG4gICAgICAgIHJldHVybiBuZXdLZXlzO1xuICAgICAgfSk7XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvdXNlci1hcGkta2V5cy8ke2tleUlkfWAsIHtcbiAgICAgICAgbWV0aG9kOiAnREVMRVRFJyxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIC8vIFJldmVydCB0aGUgb3B0aW1pc3RpYyB1cGRhdGUgb24gZXJyb3JcbiAgICAgICAgc2V0QXBpS2V5cyhwcmV2aW91c0tleXMpO1xuICAgICAgICBjb25zdCBlcnJvciA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yLmVycm9yIHx8ICdGYWlsZWQgdG8gcmV2b2tlIEFQSSBrZXknKTtcbiAgICAgIH1cblxuICAgICAgdG9hc3Quc3VjY2VzcygnQVBJIGtleSByZXZva2VkIHN1Y2Nlc3NmdWxseScpO1xuICAgICAgLy8gUmVmcmVzaCB0byBlbnN1cmUgY29ycmVjdCBjb3VudHMgYW5kIGxhdGVzdCBkYXRhXG4gICAgICBhd2FpdCBmZXRjaEFwaUtleXMoKTtcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciByZXZva2luZyBBUEkga2V5OicsIGVycm9yKTtcbiAgICAgIHRvYXN0LmVycm9yKGVycm9yLm1lc3NhZ2UgfHwgJ0ZhaWxlZCB0byByZXZva2UgQVBJIGtleScpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVWaWV3VXNhZ2UgPSAoa2V5SWQ6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IGFwaUtleSA9IGFwaUtleXMuZmluZChrZXkgPT4ga2V5LmlkID09PSBrZXlJZCk7XG4gICAgc2V0U2VsZWN0ZWRBcGlLZXkoYXBpS2V5KTtcbiAgICBzZXRTaG93VXNhZ2VEaWFsb2codHJ1ZSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQ3JlYXRlRGlhbG9nQ2xvc2UgPSAob3BlbjogYm9vbGVhbikgPT4ge1xuICAgIHNldFNob3dDcmVhdGVEaWFsb2cob3Blbik7XG4gICAgLy8gTm90ZTogV2Ugbm8gbG9uZ2VyIG5lZWQgdG8gcmVmcmVzaCBoZXJlIHNpbmNlIHdlIHJlZnJlc2ggaW1tZWRpYXRlbHkgYWZ0ZXIgY3JlYXRpb25cbiAgfTtcblxuICBjb25zdCBjYW5DcmVhdGVNb3JlS2V5cyA9IHN1YnNjcmlwdGlvbkluZm8gXG4gICAgPyBzdWJzY3JpcHRpb25JbmZvLmN1cnJlbnRDb3VudCA8IHN1YnNjcmlwdGlvbkluZm8ua2V5TGltaXQgXG4gICAgOiB0cnVlO1xuXG4gIGlmIChsb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxDYXJkPlxuICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcHktOFwiPlxuICAgICAgICAgIDxSZWZyZXNoQ3cgY2xhc3NOYW1lPVwiaC02IHctNiBhbmltYXRlLXNwaW4gbXItMlwiIC8+XG4gICAgICAgICAgTG9hZGluZyBBUEkga2V5cy4uLlxuICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgPC9DYXJkPlxuICAgICk7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICB7LyogSGVhZGVyICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICA8S2V5IGNsYXNzTmFtZT1cImgtNiB3LTZcIiAvPlxuICAgICAgICAgICAgQVBJIEtleXNcbiAgICAgICAgICA8L2gyPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbXQtMVwiPlxuICAgICAgICAgICAgR2VuZXJhdGUgQVBJIGtleXMgZm9yIHByb2dyYW1tYXRpYyBhY2Nlc3MgdG8ge2NvbmZpZ05hbWV9XG4gICAgICAgICAgPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAge2NhbkNyZWF0ZU1vcmVLZXlzID8gKFxuICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dDcmVhdGVEaWFsb2codHJ1ZSl9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFBsdXMgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICBDcmVhdGUgQVBJIEtleVxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICApIDogKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1lbmQgZ2FwLTJcIj5cbiAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgZGlzYWJsZWRcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgb3BhY2l0eS01MFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICBDcmVhdGUgQVBJIEtleVxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtb3JhbmdlLTYwMCBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICB7c3Vic2NyaXB0aW9uSW5mbz8udGllciA9PT0gJ2ZyZWUnXG4gICAgICAgICAgICAgICAgPyAnVXBncmFkZSB0byBTdGFydGVyIHBsYW4gZm9yIG1vcmUgQVBJIGtleXMnXG4gICAgICAgICAgICAgICAgOiAnQVBJIGtleSBsaW1pdCByZWFjaGVkIC0gdXBncmFkZSBmb3IgdW5saW1pdGVkIGtleXMnXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogQVBJIEtleSBVc2FnZSBMaW1pdHMgKi99XG4gICAgICB7c3Vic2NyaXB0aW9uSW5mbyAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHAtNFwiPlxuICAgICAgICAgIDxMaW1pdEluZGljYXRvclxuICAgICAgICAgICAgY3VycmVudD17c3Vic2NyaXB0aW9uSW5mby5jdXJyZW50Q291bnR9XG4gICAgICAgICAgICBsaW1pdD17c3Vic2NyaXB0aW9uSW5mby5rZXlMaW1pdH1cbiAgICAgICAgICAgIGxhYmVsPVwiVXNlci1HZW5lcmF0ZWQgQVBJIEtleXNcIlxuICAgICAgICAgICAgdGllcj17c3Vic2NyaXB0aW9uSW5mby50aWVyIGFzIGFueX1cbiAgICAgICAgICAgIHNob3dVcGdyYWRlSGludD17dHJ1ZX1cbiAgICAgICAgICAvPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBBUEkgS2V5cyBMaXN0ICovfVxuICAgICAge2FwaUtleXMubGVuZ3RoID09PSAwID8gKFxuICAgICAgICA8Q2FyZD5cbiAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktOFwiPlxuICAgICAgICAgICAgPEtleSBjbGFzc05hbWU9XCJoLTEyIHctMTIgbXgtYXV0byB0ZXh0LWdyYXktNDAwIG1iLTRcIiAvPlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCBtYi0yXCI+Tm8gQVBJIEtleXM8L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtYi00XCI+XG4gICAgICAgICAgICAgIENyZWF0ZSB5b3VyIGZpcnN0IEFQSSBrZXkgdG8gc3RhcnQgdXNpbmcgdGhlIFJvdUtleSBBUEkgcHJvZ3JhbW1hdGljYWxseS5cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIHtjYW5DcmVhdGVNb3JlS2V5cyA/IChcbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dDcmVhdGVEaWFsb2codHJ1ZSl9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8UGx1cyBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgIENyZWF0ZSBZb3VyIEZpcnN0IEFQSSBLZXlcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgPEJ1dHRvbiBkaXNhYmxlZCBjbGFzc05hbWU9XCJvcGFjaXR5LTUwXCI+XG4gICAgICAgICAgICAgICAgICA8UGx1cyBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgQ3JlYXRlIFlvdXIgRmlyc3QgQVBJIEtleVxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1vcmFuZ2UtNjAwIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICB7c3Vic2NyaXB0aW9uSW5mbz8udGllciA9PT0gJ2ZyZWUnXG4gICAgICAgICAgICAgICAgICAgID8gJ1VwZ3JhZGUgdG8gU3RhcnRlciBwbGFuIHRvIGNyZWF0ZSBBUEkga2V5cydcbiAgICAgICAgICAgICAgICAgICAgOiAnQVBJIGtleSBsaW1pdCByZWFjaGVkIC0gdXBncmFkZSBmb3IgdW5saW1pdGVkIGtleXMnXG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgPC9DYXJkPlxuICAgICAgKSA6IChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdhcC00XCI+XG4gICAgICAgICAge2FwaUtleXMubWFwKChhcGlLZXkpID0+IChcbiAgICAgICAgICAgIDxBcGlLZXlDYXJkXG4gICAgICAgICAgICAgIGtleT17YXBpS2V5LmlkfVxuICAgICAgICAgICAgICBhcGlLZXk9e2FwaUtleX1cbiAgICAgICAgICAgICAgb25FZGl0PXsoa2V5KSA9PiB7XG4gICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRBcGlLZXkoa2V5KTtcbiAgICAgICAgICAgICAgICBzZXRTaG93RWRpdERpYWxvZyh0cnVlKTtcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgb25SZXZva2U9e2hhbmRsZVJldm9rZUFwaUtleX1cbiAgICAgICAgICAgICAgb25WaWV3VXNhZ2U9e2hhbmRsZVZpZXdVc2FnZX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgey8qIERpYWxvZ3MgKi99XG4gICAgICA8Q3JlYXRlQXBpS2V5RGlhbG9nXG4gICAgICAgIG9wZW49e3Nob3dDcmVhdGVEaWFsb2d9XG4gICAgICAgIG9uT3BlbkNoYW5nZT17aGFuZGxlQ3JlYXRlRGlhbG9nQ2xvc2V9XG4gICAgICAgIG9uQ3JlYXRlQXBpS2V5PXtoYW5kbGVDcmVhdGVBcGlLZXl9XG4gICAgICAgIGNvbmZpZ05hbWU9e2NvbmZpZ05hbWV9XG4gICAgICAgIGNyZWF0aW5nPXtjcmVhdGluZ31cbiAgICAgICAgc3Vic2NyaXB0aW9uVGllcj17c3Vic2NyaXB0aW9uSW5mbz8udGllciB8fCAnc3RhcnRlcid9XG4gICAgICAvPlxuXG4gICAgICB7c2VsZWN0ZWRBcGlLZXkgJiYgKFxuICAgICAgICA8PlxuICAgICAgICAgIDxFZGl0QXBpS2V5RGlhbG9nXG4gICAgICAgICAgICBvcGVuPXtzaG93RWRpdERpYWxvZ31cbiAgICAgICAgICAgIG9uT3BlbkNoYW5nZT17c2V0U2hvd0VkaXREaWFsb2d9XG4gICAgICAgICAgICBhcGlLZXk9e3NlbGVjdGVkQXBpS2V5fVxuICAgICAgICAgICAgb25VcGRhdGVBcGlLZXk9eyh1cGRhdGVzKSA9PiBoYW5kbGVFZGl0QXBpS2V5KHNlbGVjdGVkQXBpS2V5LmlkLCB1cGRhdGVzKX1cbiAgICAgICAgICAvPlxuXG4gICAgICAgICAgPEFwaUtleVVzYWdlRGlhbG9nXG4gICAgICAgICAgICBvcGVuPXtzaG93VXNhZ2VEaWFsb2d9XG4gICAgICAgICAgICBvbk9wZW5DaGFuZ2U9e3NldFNob3dVc2FnZURpYWxvZ31cbiAgICAgICAgICAgIGFwaUtleT17c2VsZWN0ZWRBcGlLZXl9XG4gICAgICAgICAgLz5cbiAgICAgICAgPC8+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJCdXR0b24iLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJQbHVzIiwiS2V5IiwiUmVmcmVzaEN3IiwidG9hc3QiLCJBcGlLZXlDYXJkIiwiQ3JlYXRlQXBpS2V5RGlhbG9nIiwiRWRpdEFwaUtleURpYWxvZyIsIkFwaUtleVVzYWdlRGlhbG9nIiwiTGltaXRJbmRpY2F0b3IiLCJBcGlLZXlNYW5hZ2VyIiwiY29uZmlnSWQiLCJjb25maWdOYW1lIiwiYXBpS2V5cyIsInNldEFwaUtleXMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImNyZWF0aW5nIiwic2V0Q3JlYXRpbmciLCJzaG93Q3JlYXRlRGlhbG9nIiwic2V0U2hvd0NyZWF0ZURpYWxvZyIsInNob3dFZGl0RGlhbG9nIiwic2V0U2hvd0VkaXREaWFsb2ciLCJzaG93VXNhZ2VEaWFsb2ciLCJzZXRTaG93VXNhZ2VEaWFsb2ciLCJzZWxlY3RlZEFwaUtleSIsInNldFNlbGVjdGVkQXBpS2V5Iiwic3Vic2NyaXB0aW9uSW5mbyIsInNldFN1YnNjcmlwdGlvbkluZm8iLCJmZXRjaEFwaUtleXMiLCJyZXNwb25zZSIsImZldGNoIiwib2siLCJFcnJvciIsImRhdGEiLCJqc29uIiwiYXBpX2tleXMiLCJlcnJvciIsImNvbnNvbGUiLCJmZXRjaFN1YnNjcmlwdGlvbkluZm8iLCJjdXJyZW50S2V5Q291bnQiLCJ0aWVyUmVzcG9uc2UiLCJ0aWVyRGF0YSIsInRpZXIiLCJsaW1pdHMiLCJmcmVlIiwic3RhcnRlciIsInByb2Zlc3Npb25hbCIsImVudGVycHJpc2UiLCJrZXlDb3VudCIsInVuZGVmaW5lZCIsImxlbmd0aCIsImtleUxpbWl0IiwiY3VycmVudENvdW50IiwiaGFuZGxlQ3JlYXRlQXBpS2V5Iiwia2V5RGF0YSIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsImN1c3RvbV9hcGlfY29uZmlnX2lkIiwibmV3QXBpS2V5Iiwic3VjY2VzcyIsIm9wdGltaXN0aWNLZXkiLCJjdXN0b21fYXBpX2NvbmZpZ3MiLCJpZCIsIm5hbWUiLCJwcmV2S2V5cyIsIm5ld0tleXMiLCJtZXNzYWdlIiwiaGFuZGxlRWRpdEFwaUtleSIsImtleUlkIiwidXBkYXRlcyIsInByZXZpb3VzS2V5cyIsIm1hcCIsImtleSIsImhhbmRsZVJldm9rZUFwaUtleSIsImNvbmZpcm0iLCJmaWx0ZXIiLCJoYW5kbGVWaWV3VXNhZ2UiLCJhcGlLZXkiLCJmaW5kIiwiaGFuZGxlQ3JlYXRlRGlhbG9nQ2xvc2UiLCJvcGVuIiwiY2FuQ3JlYXRlTW9yZUtleXMiLCJjbGFzc05hbWUiLCJkaXYiLCJoMiIsInAiLCJvbkNsaWNrIiwiZGlzYWJsZWQiLCJjdXJyZW50IiwibGltaXQiLCJsYWJlbCIsInNob3dVcGdyYWRlSGludCIsImgzIiwib25FZGl0Iiwib25SZXZva2UiLCJvblZpZXdVc2FnZSIsIm9uT3BlbkNoYW5nZSIsIm9uQ3JlYXRlQXBpS2V5Iiwic3Vic2NyaXB0aW9uVGllciIsIm9uVXBkYXRlQXBpS2V5Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/UserApiKeys/ApiKeyManager.tsx\n"));

/***/ })

});