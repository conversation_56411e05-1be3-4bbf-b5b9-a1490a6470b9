"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/[configId]/page",{

/***/ "(app-pages-browser)/./src/components/UserApiKeys/ApiKeyManager.tsx":
/*!******************************************************!*\
  !*** ./src/components/UserApiKeys/ApiKeyManager.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiKeyManager: () => (/* binding */ ApiKeyManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _ApiKeyCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ApiKeyCard */ \"(app-pages-browser)/./src/components/UserApiKeys/ApiKeyCard.tsx\");\n/* harmony import */ var _CreateApiKeyDialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CreateApiKeyDialog */ \"(app-pages-browser)/./src/components/UserApiKeys/CreateApiKeyDialog.tsx\");\n/* harmony import */ var _components_TierEnforcement__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/TierEnforcement */ \"(app-pages-browser)/./src/components/TierEnforcement/index.ts\");\n/* harmony import */ var _components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/ConfirmationModal */ \"(app-pages-browser)/./src/components/ui/ConfirmationModal.tsx\");\n/* harmony import */ var _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/useConfirmation */ \"(app-pages-browser)/./src/hooks/useConfirmation.ts\");\n/* __next_internal_client_entry_do_not_use__ ApiKeyManager auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction ApiKeyManager(param) {\n    let { configId, configName } = param;\n    _s();\n    const [apiKeys, setApiKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [creating, setCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCreateDialog, setShowCreateDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [subscriptionInfo, setSubscriptionInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const confirmation = (0,_hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_9__.useConfirmation)();\n    // Fetch API keys for this configuration\n    const fetchApiKeys = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/user-api-keys?config_id=\".concat(configId));\n            if (!response.ok) {\n                throw new Error('Failed to fetch API keys');\n            }\n            const data = await response.json();\n            setApiKeys(data.api_keys || []);\n        } catch (error) {\n            console.error('Error fetching API keys:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error('Failed to load API keys');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Fetch subscription info\n    const fetchSubscriptionInfo = async (currentKeyCount)=>{\n        try {\n            // Get the tier from the user's active subscription\n            const tierResponse = await fetch('/api/user/subscription-tier');\n            const tierData = tierResponse.ok ? await tierResponse.json() : null;\n            const tier = (tierData === null || tierData === void 0 ? void 0 : tierData.tier) || 'starter';\n            const limits = {\n                free: 3,\n                starter: 50,\n                professional: 999999,\n                enterprise: 999999\n            };\n            // Use provided count or current apiKeys length\n            const keyCount = currentKeyCount !== undefined ? currentKeyCount : apiKeys.length;\n            setSubscriptionInfo({\n                tier,\n                keyLimit: limits[tier] || limits.free,\n                currentCount: keyCount\n            });\n        } catch (error) {\n            console.error('Error fetching subscription info:', error);\n            // Fallback to free tier\n            const keyCount = currentKeyCount !== undefined ? currentKeyCount : apiKeys.length;\n            setSubscriptionInfo({\n                tier: 'free',\n                keyLimit: 3,\n                currentCount: keyCount\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ApiKeyManager.useEffect\": ()=>{\n            fetchApiKeys();\n        }\n    }[\"ApiKeyManager.useEffect\"], [\n        configId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ApiKeyManager.useEffect\": ()=>{\n            if (apiKeys.length >= 0) {\n                fetchSubscriptionInfo();\n            }\n        }\n    }[\"ApiKeyManager.useEffect\"], [\n        apiKeys\n    ]);\n    const handleCreateApiKey = async (keyData)=>{\n        try {\n            setCreating(true);\n            const response = await fetch('/api/user-api-keys', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...keyData,\n                    custom_api_config_id: configId\n                })\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                throw new Error(error.error || 'Failed to create API key');\n            }\n            const newApiKey = await response.json();\n            // Show the full API key in a special dialog since it's only shown once\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success('API key created successfully!');\n            // Optimistically add the new key to the list immediately\n            const optimisticKey = {\n                ...newApiKey,\n                // Add any missing fields that might be needed for display\n                custom_api_configs: {\n                    id: configId,\n                    name: configName\n                }\n            };\n            setApiKeys((prevKeys)=>{\n                const newKeys = [\n                    optimisticKey,\n                    ...prevKeys\n                ];\n                // Immediately update subscription info with new count\n                fetchSubscriptionInfo(newKeys.length);\n                return newKeys;\n            });\n            // Also refresh to ensure we have the latest data and correct counts\n            await fetchApiKeys();\n            return newApiKey;\n        } catch (error) {\n            console.error('Error creating API key:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(error.message || 'Failed to create API key');\n            throw error;\n        } finally{\n            setCreating(false);\n        }\n    };\n    const handleRevokeApiKey = async (keyId)=>{\n        const apiKey = apiKeys.find((key)=>key.id === keyId);\n        const keyName = (apiKey === null || apiKey === void 0 ? void 0 : apiKey.key_name) || 'this API key';\n        confirmation.showConfirmation({\n            title: 'Revoke API Key',\n            message: 'Are you sure you want to revoke \"'.concat(keyName, '\"? This action cannot be undone and will immediately disable the key.'),\n            confirmText: 'Revoke Key',\n            cancelText: 'Cancel',\n            type: 'danger'\n        }, async ()=>{\n            try {\n                const response = await fetch(\"/api/user-api-keys/\".concat(keyId), {\n                    method: 'DELETE'\n                });\n                if (!response.ok) {\n                    const error = await response.json();\n                    throw new Error(error.error || 'Failed to revoke API key');\n                }\n                // Update the key status to revoked immediately after successful API call\n                setApiKeys((prevKeys)=>prevKeys.map((key)=>key.id === keyId ? {\n                            ...key,\n                            status: 'revoked'\n                        } : key));\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success('API key revoked successfully');\n            } catch (error) {\n                console.error('Error revoking API key:', error);\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(error.message || 'Failed to revoke API key');\n                throw error; // Re-throw to let the confirmation modal handle the error state\n            }\n        });\n    };\n    const handleCreateDialogClose = (open)=>{\n        setShowCreateDialog(open);\n    // Note: We no longer need to refresh here since we refresh immediately after creation\n    };\n    const canCreateMoreKeys = subscriptionInfo ? subscriptionInfo.currentCount < subscriptionInfo.keyLimit : true;\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"flex items-center justify-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-6 w-6 animate-spin mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, this),\n                    \"Loading API keys...\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 208,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n            lineNumber: 207,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"API Keys\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: [\n                                    \"Generate API keys for programmatic access to \",\n                                    configName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this),\n                    canCreateMoreKeys ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: ()=>setShowCreateDialog(true),\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this),\n                            \"Create API Key\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-end gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                disabled: true,\n                                className: \"flex items-center gap-2 opacity-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Create API Key\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-orange-600 font-medium\",\n                                children: (subscriptionInfo === null || subscriptionInfo === void 0 ? void 0 : subscriptionInfo.tier) === 'free' ? 'Upgrade to Starter plan for more API keys' : 'API key limit reached - upgrade for unlimited keys'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            subscriptionInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg border border-gray-200 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_7__.LimitIndicator, {\n                    current: subscriptionInfo.currentCount,\n                    limit: subscriptionInfo.keyLimit,\n                    label: \"User-Generated API Keys\",\n                    tier: subscriptionInfo.tier,\n                    showUpgradeHint: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 258,\n                columnNumber: 9\n            }, this),\n            apiKeys.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"text-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            className: \"h-12 w-12 mx-auto text-gray-400 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-2\",\n                            children: \"No API Keys\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Create your first API key to start using the RouKey API programmatically.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 13\n                        }, this),\n                        canCreateMoreKeys ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: ()=>setShowCreateDialog(true),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 17\n                                }, this),\n                                \"Create Your First API Key\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    disabled: true,\n                                    className: \"opacity-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Create Your First API Key\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-orange-600 font-medium\",\n                                    children: (subscriptionInfo === null || subscriptionInfo === void 0 ? void 0 : subscriptionInfo.tier) === 'free' ? 'Upgrade to Starter plan to create API keys' : 'API key limit reached - upgrade for unlimited keys'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 271,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4\",\n                children: apiKeys.map((apiKey)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ApiKeyCard__WEBPACK_IMPORTED_MODULE_5__.ApiKeyCard, {\n                        apiKey: apiKey,\n                        onRevoke: handleRevokeApiKey\n                    }, apiKey.id, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 302,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateApiKeyDialog__WEBPACK_IMPORTED_MODULE_6__.CreateApiKeyDialog, {\n                open: showCreateDialog,\n                onOpenChange: handleCreateDialogClose,\n                onCreateApiKey: handleCreateApiKey,\n                configName: configName,\n                creating: creating,\n                subscriptionTier: (subscriptionInfo === null || subscriptionInfo === void 0 ? void 0 : subscriptionInfo.tier) || 'starter'\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 314,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: confirmation.isOpen,\n                onClose: confirmation.hideConfirmation,\n                onConfirm: confirmation.onConfirm,\n                title: confirmation.title,\n                message: confirmation.message,\n                confirmText: confirmation.confirmText,\n                cancelText: confirmation.cancelText,\n                type: confirmation.type,\n                isLoading: confirmation.isLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 324,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, this);\n}\n_s(ApiKeyManager, \"wzx+xNoPucKu2oL+bFaGPsR3hPI=\", false, function() {\n    return [\n        _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_9__.useConfirmation\n    ];\n});\n_c = ApiKeyManager;\nvar _c;\n$RefreshReg$(_c, \"ApiKeyManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/UserApiKeys/ApiKeyManager.tsx\n"));

/***/ })

});