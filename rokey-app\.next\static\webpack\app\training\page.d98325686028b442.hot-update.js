"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/training/page",{

/***/ "(app-pages-browser)/./src/components/TierEnforcement/UpgradePrompt.tsx":
/*!**********************************************************!*\
  !*** ./src/components/TierEnforcement/UpgradePrompt.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UpgradePrompt: () => (/* binding */ UpgradePrompt)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_LockClosedIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,LockClosedIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LockClosedIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_LockClosedIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,LockClosedIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_LockClosedIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,LockClosedIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _lib_stripe_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stripe-client */ \"(app-pages-browser)/./src/lib/stripe-client.ts\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ UpgradePrompt auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst featureDisplayNames = {\n    custom_roles: 'Custom Roles',\n    knowledge_base: 'Knowledge Base',\n    advanced_routing: 'Advanced Routing',\n    prompt_engineering: 'Prompt Engineering',\n    semantic_caching: 'Semantic Caching',\n    configurations: 'API Configurations'\n};\nconst getMinimumTierForFeature = (feature)=>{\n    const tiers = [\n        'starter',\n        'professional',\n        'enterprise'\n    ];\n    for (const tier of tiers){\n        const config = _lib_stripe_client__WEBPACK_IMPORTED_MODULE_2__.TIER_CONFIGS[tier];\n        switch(feature){\n            case 'custom_roles':\n                if (config.limits.canUseCustomRoles) return tier;\n                break;\n            case 'knowledge_base':\n                if (config.limits.canUseKnowledgeBase) return tier;\n                break;\n            case 'advanced_routing':\n                if (config.limits.canUseAdvancedRouting) return tier;\n                break;\n            case 'prompt_engineering':\n                if (config.limits.canUsePromptEngineering) return tier;\n                break;\n            case 'semantic_caching':\n                if (config.limits.canUseSemanticCaching) return tier;\n                break;\n            case 'configurations':\n                // For configurations, find the tier that has more configurations than free tier\n                if (config.limits.configurations > _lib_stripe_client__WEBPACK_IMPORTED_MODULE_2__.TIER_CONFIGS.free.limits.configurations) return tier;\n                break;\n        }\n    }\n    return 'starter';\n};\nfunction UpgradePrompt(param) {\n    let { feature, currentTier, customMessage, size = 'md', variant = 'card' } = param;\n    _s();\n    const { createCheckoutSession } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__.useSubscription)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const minimumTier = getMinimumTierForFeature(feature);\n    const minimumTierConfig = _lib_stripe_client__WEBPACK_IMPORTED_MODULE_2__.TIER_CONFIGS[minimumTier];\n    const featureName = featureDisplayNames[feature];\n    const handleUpgrade = async ()=>{\n        try {\n            if (minimumTier === 'starter') {\n                await createCheckoutSession('starter');\n            } else if (minimumTier === 'professional') {\n                await createCheckoutSession('professional');\n            } else {\n                router.push('/pricing');\n            }\n        } catch (error) {\n            console.error('Error creating checkout session:', error);\n            router.push('/pricing');\n        }\n    };\n    const sizeClasses = {\n        sm: 'p-4 text-sm',\n        md: 'p-6 text-base',\n        lg: 'p-8 text-lg'\n    };\n    const variantClasses = {\n        card: 'bg-gradient-to-br from-orange-50 to-orange-100 border border-orange-200 rounded-xl shadow-sm',\n        banner: 'bg-orange-100 border-l-4 border-orange-500 rounded-r-lg',\n        inline: 'bg-orange-50 border border-orange-200 rounded-lg'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        className: \"\".concat(variantClasses[variant], \" \").concat(sizeClasses[size]),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_LockClosedIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                            children: [\n                                featureName,\n                                \" - Premium Feature\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-700 mb-4\",\n                            children: customMessage || \"\".concat(featureName, \" is available starting with the \").concat(minimumTierConfig.name, \" plan. \\n               Upgrade to unlock this powerful feature and enhance your RouKey experience.\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleUpgrade,\n                                    className: \"inline-flex items-center px-4 py-2 bg-orange-600 text-white text-sm font-medium rounded-lg hover:bg-orange-700 transition-colors duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_LockClosedIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Upgrade to \",\n                                        minimumTierConfig.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/pricing'),\n                                    className: \"inline-flex items-center px-4 py-2 text-orange-600 text-sm font-medium hover:text-orange-700 transition-colors duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_LockClosedIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"View All Plans\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n            lineNumber: 103,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, this);\n}\n_s(UpgradePrompt, \"mQL/U3l0hNJhYvttzby62V49684=\", false, function() {\n    return [\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__.useSubscription,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = UpgradePrompt;\nvar _c;\n$RefreshReg$(_c, \"UpgradePrompt\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TierEnforcement/UpgradePrompt.tsx\n"));

/***/ })

});