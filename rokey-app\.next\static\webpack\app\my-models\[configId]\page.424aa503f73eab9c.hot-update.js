"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/[configId]/page",{

/***/ "(app-pages-browser)/./src/components/UserApiKeys/ApiKeyManager.tsx":
/*!******************************************************!*\
  !*** ./src/components/UserApiKeys/ApiKeyManager.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiKeyManager: () => (/* binding */ ApiKeyManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _ApiKeyCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ApiKeyCard */ \"(app-pages-browser)/./src/components/UserApiKeys/ApiKeyCard.tsx\");\n/* harmony import */ var _CreateApiKeyDialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CreateApiKeyDialog */ \"(app-pages-browser)/./src/components/UserApiKeys/CreateApiKeyDialog.tsx\");\n/* harmony import */ var _EditApiKeyDialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./EditApiKeyDialog */ \"(app-pages-browser)/./src/components/UserApiKeys/EditApiKeyDialog.tsx\");\n/* harmony import */ var _ApiKeyUsageDialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ApiKeyUsageDialog */ \"(app-pages-browser)/./src/components/UserApiKeys/ApiKeyUsageDialog.tsx\");\n/* harmony import */ var _components_TierEnforcement__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/TierEnforcement */ \"(app-pages-browser)/./src/components/TierEnforcement/index.ts\");\n/* __next_internal_client_entry_do_not_use__ ApiKeyManager auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction ApiKeyManager(param) {\n    let { configId, configName } = param;\n    _s();\n    const [apiKeys, setApiKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [creating, setCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCreateDialog, setShowCreateDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditDialog, setShowEditDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showUsageDialog, setShowUsageDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedApiKey, setSelectedApiKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [subscriptionInfo, setSubscriptionInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch API keys for this configuration\n    const fetchApiKeys = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/user-api-keys?config_id=\".concat(configId));\n            if (!response.ok) {\n                throw new Error('Failed to fetch API keys');\n            }\n            const data = await response.json();\n            setApiKeys(data.api_keys || []);\n        } catch (error) {\n            console.error('Error fetching API keys:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error('Failed to load API keys');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Fetch subscription info\n    const fetchSubscriptionInfo = async ()=>{\n        try {\n            // Get the tier from the user's active subscription\n            const tierResponse = await fetch('/api/user/subscription-tier');\n            const tierData = tierResponse.ok ? await tierResponse.json() : null;\n            const tier = (tierData === null || tierData === void 0 ? void 0 : tierData.tier) || 'starter';\n            const limits = {\n                free: 3,\n                starter: 50,\n                professional: 999999,\n                enterprise: 999999\n            };\n            setSubscriptionInfo({\n                tier,\n                keyLimit: limits[tier] || limits.free,\n                currentCount: apiKeys.length\n            });\n        } catch (error) {\n            console.error('Error fetching subscription info:', error);\n            // Fallback to free tier\n            setSubscriptionInfo({\n                tier: 'free',\n                keyLimit: 3,\n                currentCount: apiKeys.length\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ApiKeyManager.useEffect\": ()=>{\n            fetchApiKeys();\n        }\n    }[\"ApiKeyManager.useEffect\"], [\n        configId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ApiKeyManager.useEffect\": ()=>{\n            if (apiKeys.length >= 0) {\n                fetchSubscriptionInfo();\n            }\n        }\n    }[\"ApiKeyManager.useEffect\"], [\n        apiKeys\n    ]);\n    const handleCreateApiKey = async (keyData)=>{\n        try {\n            setCreating(true);\n            const response = await fetch('/api/user-api-keys', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...keyData,\n                    custom_api_config_id: configId\n                })\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                throw new Error(error.error || 'Failed to create API key');\n            }\n            const newApiKey = await response.json();\n            // Show the full API key in a special dialog since it's only shown once\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success('API key created successfully!');\n            // Optimistically add the new key to the list immediately\n            const optimisticKey = {\n                ...newApiKey,\n                // Add any missing fields that might be needed for display\n                custom_api_configs: {\n                    id: configId,\n                    name: configName\n                }\n            };\n            setApiKeys((prevKeys)=>[\n                    optimisticKey,\n                    ...prevKeys\n                ]);\n            // Also refresh to ensure we have the latest data and correct counts\n            await fetchApiKeys();\n            return newApiKey;\n        } catch (error) {\n            console.error('Error creating API key:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(error.message || 'Failed to create API key');\n            throw error;\n        } finally{\n            setCreating(false);\n        }\n    };\n    const handleEditApiKey = async (keyId, updates)=>{\n        try {\n            const response = await fetch(\"/api/user-api-keys/\".concat(keyId), {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(updates)\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                throw new Error(error.error || 'Failed to update API key');\n            }\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success('API key updated successfully');\n            await fetchApiKeys();\n            setShowEditDialog(false);\n            setSelectedApiKey(null);\n        } catch (error) {\n            console.error('Error updating API key:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(error.message || 'Failed to update API key');\n        }\n    };\n    const handleRevokeApiKey = async (keyId)=>{\n        if (!confirm('Are you sure you want to revoke this API key? This action cannot be undone.')) {\n            return;\n        }\n        try {\n            // Optimistically remove the key from the list\n            const previousKeys = [\n                ...apiKeys\n            ];\n            setApiKeys((prevKeys)=>prevKeys.filter((key)=>key.id !== keyId));\n            const response = await fetch(\"/api/user-api-keys/\".concat(keyId), {\n                method: 'DELETE'\n            });\n            if (!response.ok) {\n                // Revert the optimistic update on error\n                setApiKeys(previousKeys);\n                const error = await response.json();\n                throw new Error(error.error || 'Failed to revoke API key');\n            }\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success('API key revoked successfully');\n            // Refresh to ensure correct counts and latest data\n            await fetchApiKeys();\n        } catch (error) {\n            console.error('Error revoking API key:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(error.message || 'Failed to revoke API key');\n        }\n    };\n    const handleViewUsage = (keyId)=>{\n        const apiKey = apiKeys.find((key)=>key.id === keyId);\n        setSelectedApiKey(apiKey);\n        setShowUsageDialog(true);\n    };\n    const handleCreateDialogClose = (open)=>{\n        setShowCreateDialog(open);\n    // Note: We no longer need to refresh here since we refresh immediately after creation\n    };\n    const canCreateMoreKeys = subscriptionInfo ? subscriptionInfo.currentCount < subscriptionInfo.keyLimit : true;\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"flex items-center justify-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-6 w-6 animate-spin mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, this),\n                    \"Loading API keys...\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 217,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n            lineNumber: 216,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"API Keys\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: [\n                                    \"Generate API keys for programmatic access to \",\n                                    configName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this),\n                    canCreateMoreKeys ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: ()=>setShowCreateDialog(true),\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 13\n                            }, this),\n                            \"Create API Key\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-end gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                disabled: true,\n                                className: \"flex items-center gap-2 opacity-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Create API Key\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-orange-600 font-medium\",\n                                children: (subscriptionInfo === null || subscriptionInfo === void 0 ? void 0 : subscriptionInfo.tier) === 'free' ? 'Upgrade to Starter plan for more API keys' : 'API key limit reached - upgrade for unlimited keys'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, this),\n            subscriptionInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg border border-gray-200 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_9__.LimitIndicator, {\n                    current: subscriptionInfo.currentCount,\n                    limit: subscriptionInfo.keyLimit,\n                    label: \"User-Generated API Keys\",\n                    tier: subscriptionInfo.tier,\n                    showUpgradeHint: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 267,\n                columnNumber: 9\n            }, this),\n            apiKeys.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"text-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            className: \"h-12 w-12 mx-auto text-gray-400 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-2\",\n                            children: \"No API Keys\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Create your first API key to start using the RouKey API programmatically.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 13\n                        }, this),\n                        canCreateMoreKeys ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: ()=>setShowCreateDialog(true),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 17\n                                }, this),\n                                \"Create Your First API Key\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    disabled: true,\n                                    className: \"opacity-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Create Your First API Key\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-orange-600 font-medium\",\n                                    children: (subscriptionInfo === null || subscriptionInfo === void 0 ? void 0 : subscriptionInfo.tier) === 'free' ? 'Upgrade to Starter plan to create API keys' : 'API key limit reached - upgrade for unlimited keys'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                    lineNumber: 281,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 280,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4\",\n                children: apiKeys.map((apiKey)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ApiKeyCard__WEBPACK_IMPORTED_MODULE_5__.ApiKeyCard, {\n                        apiKey: apiKey,\n                        onEdit: (key)=>{\n                            setSelectedApiKey(key);\n                            setShowEditDialog(true);\n                        },\n                        onRevoke: handleRevokeApiKey,\n                        onViewUsage: handleViewUsage\n                    }, apiKey.id, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 311,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateApiKeyDialog__WEBPACK_IMPORTED_MODULE_6__.CreateApiKeyDialog, {\n                open: showCreateDialog,\n                onOpenChange: handleCreateDialogClose,\n                onCreateApiKey: handleCreateApiKey,\n                configName: configName,\n                creating: creating,\n                subscriptionTier: (subscriptionInfo === null || subscriptionInfo === void 0 ? void 0 : subscriptionInfo.tier) || 'starter'\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 328,\n                columnNumber: 7\n            }, this),\n            selectedApiKey && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EditApiKeyDialog__WEBPACK_IMPORTED_MODULE_7__.EditApiKeyDialog, {\n                        open: showEditDialog,\n                        onOpenChange: setShowEditDialog,\n                        apiKey: selectedApiKey,\n                        onUpdateApiKey: (updates)=>handleEditApiKey(selectedApiKey.id, updates)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ApiKeyUsageDialog__WEBPACK_IMPORTED_MODULE_8__.ApiKeyUsageDialog, {\n                        open: showUsageDialog,\n                        onOpenChange: setShowUsageDialog,\n                        apiKey: selectedApiKey\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n        lineNumber: 226,\n        columnNumber: 5\n    }, this);\n}\n_s(ApiKeyManager, \"FbaUjxeLQ7jhWAVpAmW1g8u12LY=\");\n_c = ApiKeyManager;\nvar _c;\n$RefreshReg$(_c, \"ApiKeyManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/UserApiKeys/ApiKeyManager.tsx\n"));

/***/ })

});