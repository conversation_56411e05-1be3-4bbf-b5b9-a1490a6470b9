"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/page",{

/***/ "(app-pages-browser)/./src/components/TierEnforcement/UpgradePrompt.tsx":
/*!**********************************************************!*\
  !*** ./src/components/TierEnforcement/UpgradePrompt.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UpgradePrompt: () => (/* binding */ UpgradePrompt)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_LockClosedIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,LockClosedIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LockClosedIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_LockClosedIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,LockClosedIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_LockClosedIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,LockClosedIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _lib_stripe_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stripe-client */ \"(app-pages-browser)/./src/lib/stripe-client.ts\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ UpgradePrompt auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst featureDisplayNames = {\n    custom_roles: 'Custom Roles',\n    knowledge_base: 'Knowledge Base',\n    advanced_routing: 'Advanced Routing',\n    prompt_engineering: 'Prompt Engineering',\n    semantic_caching: 'Semantic Caching',\n    configurations: 'API Configurations'\n};\nconst getMinimumTierForFeature = (feature)=>{\n    const tiers = [\n        'starter',\n        'professional',\n        'enterprise'\n    ];\n    for (const tier of tiers){\n        const config = _lib_stripe_client__WEBPACK_IMPORTED_MODULE_2__.TIER_CONFIGS[tier];\n        switch(feature){\n            case 'custom_roles':\n                if (config.limits.canUseCustomRoles) return tier;\n                break;\n            case 'knowledge_base':\n                if (config.limits.canUseKnowledgeBase) return tier;\n                break;\n            case 'advanced_routing':\n                if (config.limits.canUseAdvancedRouting) return tier;\n                break;\n            case 'prompt_engineering':\n                if (config.limits.canUsePromptEngineering) return tier;\n                break;\n            case 'semantic_caching':\n                if (config.limits.canUseSemanticCaching) return tier;\n                break;\n        }\n    }\n    return 'starter';\n};\nfunction UpgradePrompt(param) {\n    let { feature, currentTier, customMessage, size = 'md', variant = 'card' } = param;\n    _s();\n    const { createCheckoutSession } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__.useSubscription)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const minimumTier = getMinimumTierForFeature(feature);\n    const minimumTierConfig = _lib_stripe_client__WEBPACK_IMPORTED_MODULE_2__.TIER_CONFIGS[minimumTier];\n    const featureName = featureDisplayNames[feature];\n    const handleUpgrade = async ()=>{\n        try {\n            if (minimumTier === 'starter') {\n                await createCheckoutSession('starter');\n            } else if (minimumTier === 'professional') {\n                await createCheckoutSession('professional');\n            } else {\n                router.push('/pricing');\n            }\n        } catch (error) {\n            console.error('Error creating checkout session:', error);\n            router.push('/pricing');\n        }\n    };\n    const sizeClasses = {\n        sm: 'p-4 text-sm',\n        md: 'p-6 text-base',\n        lg: 'p-8 text-lg'\n    };\n    const variantClasses = {\n        card: 'bg-gradient-to-br from-orange-50 to-orange-100 border border-orange-200 rounded-xl shadow-sm',\n        banner: 'bg-orange-100 border-l-4 border-orange-500 rounded-r-lg',\n        inline: 'bg-orange-50 border border-orange-200 rounded-lg'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        className: \"\".concat(variantClasses[variant], \" \").concat(sizeClasses[size]),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_LockClosedIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-5 h-5 text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                            children: [\n                                featureName,\n                                \" - Premium Feature\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-700 mb-4\",\n                            children: customMessage || \"\".concat(featureName, \" is available starting with the \").concat(minimumTierConfig.name, \" plan. \\n               Upgrade to unlock this powerful feature and enhance your RouKey experience.\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleUpgrade,\n                                    className: \"inline-flex items-center px-4 py-2 bg-orange-600 text-white text-sm font-medium rounded-lg hover:bg-orange-700 transition-colors duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_LockClosedIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Upgrade to \",\n                                        minimumTierConfig.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/pricing'),\n                                    className: \"inline-flex items-center px-4 py-2 text-orange-600 text-sm font-medium hover:text-orange-700 transition-colors duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_LockClosedIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"View All Plans\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\UpgradePrompt.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n_s(UpgradePrompt, \"mQL/U3l0hNJhYvttzby62V49684=\", false, function() {\n    return [\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_3__.useSubscription,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = UpgradePrompt;\nvar _c;\n$RefreshReg$(_c, \"UpgradePrompt\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TierEnforcement/UpgradePrompt.tsx\n"));

/***/ })

});