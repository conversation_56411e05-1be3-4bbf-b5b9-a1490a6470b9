/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/user-api-keys/[keyId]/route";
exports.ids = ["app/api/user-api-keys/[keyId]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser-api-keys%2F%5BkeyId%5D%2Froute&page=%2Fapi%2Fuser-api-keys%2F%5BkeyId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser-api-keys%2F%5BkeyId%5D%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser-api-keys%2F%5BkeyId%5D%2Froute&page=%2Fapi%2Fuser-api-keys%2F%5BkeyId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser-api-keys%2F%5BkeyId%5D%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_user_api_keys_keyId_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/user-api-keys/[keyId]/route.ts */ \"(rsc)/./src/app/api/user-api-keys/[keyId]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/user-api-keys/[keyId]/route\",\n        pathname: \"/api/user-api-keys/[keyId]\",\n        filename: \"route\",\n        bundlePath: \"app/api/user-api-keys/[keyId]/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\user-api-keys\\\\[keyId]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_user_api_keys_keyId_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser-api-keys%2F%5BkeyId%5D%2Froute&page=%2Fapi%2Fuser-api-keys%2F%5BkeyId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser-api-keys%2F%5BkeyId%5D%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/user-api-keys/[keyId]/route.ts":
/*!****************************************************!*\
  !*** ./src/app/api/user-api-keys/[keyId]/route.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_userApiKeys_apiKeyGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/userApiKeys/apiKeyGenerator */ \"(rsc)/./src/lib/userApiKeys/apiKeyGenerator.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n\n\n\n// Validation schema for updating API keys\nconst UpdateApiKeySchema = zod__WEBPACK_IMPORTED_MODULE_3__.z.object({\n    key_name: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().min(1).max(100).optional(),\n    status: zod__WEBPACK_IMPORTED_MODULE_3__.z[\"enum\"]([\n        'active',\n        'inactive'\n    ]).optional(),\n    expires_at: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().datetime().nullable().optional()\n});\n// GET /api/user-api-keys/[keyId] - Get specific API key details\nasync function GET(request, { params }) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    const { keyId } = await params;\n    try {\n        // Authenticate user\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Fetch the API key\n        const { data: apiKey, error } = await supabase.from('user_generated_api_keys').select(`\n        id,\n        key_name,\n        key_prefix,\n        encrypted_key_suffix,\n        permissions,\n        allowed_ips,\n        allowed_domains,\n        total_requests,\n        last_used_at,\n        last_used_ip,\n        status,\n        expires_at,\n        created_at,\n        updated_at,\n        custom_api_configs!inner(\n          id,\n          name\n        )\n      `).eq('id', keyId).eq('user_id', user.id).single();\n        if (error || !apiKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'API key not found'\n            }, {\n                status: 404\n            });\n        }\n        // Transform the data\n        const transformedKey = {\n            ...apiKey,\n            masked_key: await _lib_userApiKeys_apiKeyGenerator__WEBPACK_IMPORTED_MODULE_2__.ApiKeyGenerator.createMaskedKey(apiKey.key_prefix, apiKey.encrypted_key_suffix),\n            // Remove sensitive data\n            encrypted_key_suffix: undefined\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            api_key: transformedKey\n        });\n    } catch (error) {\n        console.error('Error in GET /api/user-api-keys/[keyId]:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n// PATCH /api/user-api-keys/[keyId] - Update API key\nasync function PATCH(request, { params }) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    const { keyId } = await params;\n    try {\n        // Authenticate user\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Parse and validate request body\n        const body = await request.json();\n        const validatedData = UpdateApiKeySchema.parse(body);\n        // Check if user owns the API key\n        const { data: existingKey, error: fetchError } = await supabase.from('user_generated_api_keys').select('id, user_id, custom_api_config_id').eq('id', keyId).eq('user_id', user.id).single();\n        if (fetchError || !existingKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'API key not found or access denied'\n            }, {\n                status: 404\n            });\n        }\n        // Check for duplicate key name if name is being updated\n        if (validatedData.key_name) {\n            const { data: duplicateKey } = await supabase.from('user_generated_api_keys').select('id').eq('custom_api_config_id', existingKey.custom_api_config_id).eq('key_name', validatedData.key_name).eq('status', 'active').neq('id', keyId).single();\n            if (duplicateKey) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'An API key with this name already exists for this configuration'\n                }, {\n                    status: 409\n                });\n            }\n        }\n        // Update the API key\n        const { data: updatedKey, error: updateError } = await supabase.from('user_generated_api_keys').update({\n            ...validatedData,\n            updated_at: new Date().toISOString()\n        }).eq('id', keyId).eq('user_id', user.id).select(`\n        id,\n        key_name,\n        key_prefix,\n        encrypted_key_suffix,\n        permissions,\n        allowed_ips,\n        allowed_domains,\n        total_requests,\n        last_used_at,\n        status,\n        expires_at,\n        created_at,\n        updated_at\n      `).single();\n        if (updateError) {\n            console.error('Error updating API key:', updateError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to update API key'\n            }, {\n                status: 500\n            });\n        }\n        // Transform the data\n        const transformedKey = {\n            ...updatedKey,\n            masked_key: await _lib_userApiKeys_apiKeyGenerator__WEBPACK_IMPORTED_MODULE_2__.ApiKeyGenerator.createMaskedKey(updatedKey.key_prefix, updatedKey.encrypted_key_suffix),\n            // Remove sensitive data\n            encrypted_key_suffix: undefined\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            api_key: transformedKey\n        });\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_3__.z.ZodError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid request data',\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        console.error('Error in PATCH /api/user-api-keys/[keyId]:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE /api/user-api-keys/[keyId] - Revoke API key\nasync function DELETE(request, { params }) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    const { keyId } = await params;\n    try {\n        // Authenticate user\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Check if user owns the API key\n        const { data: existingKey, error: fetchError } = await supabase.from('user_generated_api_keys').select('id, user_id, key_name').eq('id', keyId).eq('user_id', user.id).single();\n        if (fetchError || !existingKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'API key not found or access denied'\n            }, {\n                status: 404\n            });\n        }\n        // Update the API key status to revoked instead of deleting\n        const { error: revokeError } = await supabase.from('user_generated_api_keys').update({\n            status: 'revoked',\n            updated_at: new Date().toISOString()\n        }).eq('id', keyId).eq('user_id', user.id);\n        if (revokeError) {\n            console.error('Error revoking API key:', revokeError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to revoke API key'\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'API key revoked successfully'\n        }, {\n            status: 200\n        });\n    } catch (error) {\n        console.error('Error in DELETE /api/user-api-keys/[keyId]:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/user-api-keys/[keyId]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/encryption.ts":
/*!*******************************!*\
  !*** ./src/lib/encryption.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decrypt: () => (/* binding */ decrypt),\n/* harmony export */   encrypt: () => (/* binding */ encrypt)\n/* harmony export */ });\n// Web Crypto API compatible encryption for Edge Runtime\nconst ALGORITHM = 'AES-GCM';\nconst IV_LENGTH = 12; // Recommended for GCM\n// Ensure your ROKEY_ENCRYPTION_KEY is a 64-character hex string (32 bytes)\nconst ROKEY_ENCRYPTION_KEY_FROM_ENV = process.env.ROKEY_ENCRYPTION_KEY;\nconsole.log('[DEBUG] ROKEY_ENCRYPTION_KEY from process.env:', ROKEY_ENCRYPTION_KEY_FROM_ENV);\nconsole.log('[DEBUG] Length:', ROKEY_ENCRYPTION_KEY_FROM_ENV?.length);\nif (!ROKEY_ENCRYPTION_KEY_FROM_ENV || ROKEY_ENCRYPTION_KEY_FROM_ENV.length !== 64) {\n    throw new Error('Invalid ROKEY_ENCRYPTION_KEY. Please set a 64-character hex string (32 bytes) for ROKEY_ENCRYPTION_KEY in your .env.local file.');\n}\n// Convert hex string to Uint8Array for Web Crypto API\nfunction hexToUint8Array(hex) {\n    const bytes = new Uint8Array(hex.length / 2);\n    for(let i = 0; i < hex.length; i += 2){\n        bytes[i / 2] = parseInt(hex.substr(i, 2), 16);\n    }\n    return bytes;\n}\n// Convert Uint8Array to hex string\nfunction uint8ArrayToHex(bytes) {\n    return Array.from(bytes, (byte)=>byte.toString(16).padStart(2, '0')).join('');\n}\nconst keyBytes = hexToUint8Array(ROKEY_ENCRYPTION_KEY_FROM_ENV);\nasync function encrypt(text) {\n    if (typeof text !== 'string' || text.length === 0) {\n        throw new Error('Encryption input must be a non-empty string.');\n    }\n    // Generate random IV\n    const iv = crypto.getRandomValues(new Uint8Array(IV_LENGTH));\n    // Import the key for Web Crypto API\n    const cryptoKey = await crypto.subtle.importKey('raw', keyBytes, {\n        name: ALGORITHM\n    }, false, [\n        'encrypt'\n    ]);\n    // Encrypt the text\n    const encodedText = new TextEncoder().encode(text);\n    const encryptedBuffer = await crypto.subtle.encrypt({\n        name: ALGORITHM,\n        iv: iv\n    }, cryptoKey, encodedText);\n    const encryptedArray = new Uint8Array(encryptedBuffer);\n    // For AES-GCM, the auth tag is included in the encrypted data (last 16 bytes)\n    const encryptedData = encryptedArray.slice(0, -16);\n    const authTag = encryptedArray.slice(-16);\n    // Return IV:authTag:encryptedData format\n    return `${uint8ArrayToHex(iv)}:${uint8ArrayToHex(authTag)}:${uint8ArrayToHex(encryptedData)}`;\n}\nasync function decrypt(encryptedText) {\n    if (typeof encryptedText !== 'string' || encryptedText.length === 0) {\n        throw new Error('Decryption input must be a non-empty string.');\n    }\n    const parts = encryptedText.split(':');\n    if (parts.length !== 3) {\n        throw new Error('Invalid encrypted text format. Expected iv:authTag:encryptedData');\n    }\n    const iv = hexToUint8Array(parts[0]);\n    const authTag = hexToUint8Array(parts[1]);\n    const encryptedData = hexToUint8Array(parts[2]);\n    if (iv.length !== IV_LENGTH) {\n        throw new Error(`Invalid IV length. Expected ${IV_LENGTH} bytes.`);\n    }\n    if (authTag.length !== 16) {\n        throw new Error(`Invalid authTag length. Expected 16 bytes.`);\n    }\n    // Import the key for Web Crypto API\n    const cryptoKey = await crypto.subtle.importKey('raw', keyBytes, {\n        name: ALGORITHM\n    }, false, [\n        'decrypt'\n    ]);\n    // Combine encrypted data and auth tag for Web Crypto API\n    const combinedData = new Uint8Array(encryptedData.length + authTag.length);\n    combinedData.set(encryptedData);\n    combinedData.set(authTag, encryptedData.length);\n    // Decrypt the data\n    const decryptedBuffer = await crypto.subtle.decrypt({\n        name: ALGORITHM,\n        iv: iv\n    }, cryptoKey, combinedData);\n    return new TextDecoder().decode(decryptedBuffer);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/encryption.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/userApiKeys/apiKeyGenerator.ts":
/*!************************************************!*\
  !*** ./src/lib/userApiKeys/apiKeyGenerator.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiKeyGenerator: () => (/* binding */ ApiKeyGenerator)\n/* harmony export */ });\n/* harmony import */ var _lib_encryption__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/encryption */ \"(rsc)/./src/lib/encryption.ts\");\n\nclass ApiKeyGenerator {\n    static{\n        this.KEY_PREFIX = 'rk_live_';\n    }\n    static{\n        this.RANDOM_PART_LENGTH = 8 // hex chars for middle part\n        ;\n    }\n    static{\n        this.SECRET_PART_LENGTH = 32 // chars for secret part\n        ;\n    }\n    /**\n   * Generates a new API key with the format: rk_live_{8_hex_chars}_{32_random_chars}\n   * @returns Object containing the full key, prefix, and secret parts\n   */ static async generateApiKey() {\n        // Generate random hex for the middle part (visible in prefix)\n        const randomBytes = crypto.getRandomValues(new Uint8Array(this.RANDOM_PART_LENGTH / 2));\n        const randomHex = Array.from(randomBytes, (byte)=>byte.toString(16).padStart(2, '0')).join('');\n        // Generate random alphanumeric for the secret part\n        const secretPart = this.generateRandomString(this.SECRET_PART_LENGTH);\n        // Construct the full key\n        const prefix = `${this.KEY_PREFIX}${randomHex}`;\n        const fullKey = `${prefix}_${secretPart}`;\n        // Generate hash for storage\n        const hash = await this.hashApiKey(fullKey);\n        return {\n            fullKey,\n            prefix,\n            secretPart,\n            hash\n        };\n    }\n    /**\n   * Generates a cryptographically secure random string\n   * @param length Length of the string to generate\n   * @returns Random alphanumeric string\n   */ static generateRandomString(length) {\n        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n        let result = '';\n        for(let i = 0; i < length; i++){\n            const randomBytes = crypto.getRandomValues(new Uint8Array(1));\n            const randomIndex = randomBytes[0] % chars.length;\n            result += chars[randomIndex];\n        }\n        return result;\n    }\n    /**\n   * Creates a SHA-256 hash of the API key for secure storage\n   * @param apiKey The full API key to hash\n   * @returns SHA-256 hash as hex string\n   */ static async hashApiKey(apiKey) {\n        const encoder = new TextEncoder();\n        const data = encoder.encode(apiKey);\n        const hashBuffer = await crypto.subtle.digest('SHA-256', data);\n        const hashArray = new Uint8Array(hashBuffer);\n        return Array.from(hashArray, (byte)=>byte.toString(16).padStart(2, '0')).join('');\n    }\n    /**\n   * Validates the format of an API key\n   * @param apiKey The API key to validate\n   * @returns True if the format is valid\n   */ static isValidFormat(apiKey) {\n        const pattern = new RegExp(`^${this.KEY_PREFIX}[a-f0-9]{${this.RANDOM_PART_LENGTH}}_[a-zA-Z0-9]{${this.SECRET_PART_LENGTH}}$`);\n        return pattern.test(apiKey);\n    }\n    /**\n   * Extracts the prefix from a full API key\n   * @param apiKey The full API key\n   * @returns The prefix part (e.g., \"rk_live_abc12345\")\n   */ static extractPrefix(apiKey) {\n        const parts = apiKey.split('_');\n        if (parts.length >= 3) {\n            return `${parts[0]}_${parts[1]}_${parts[2]}`;\n        }\n        return '';\n    }\n    /**\n   * Encrypts the suffix part of an API key for partial display\n   * @param secretPart The secret part of the API key\n   * @returns Encrypted suffix for storage\n   */ static async encryptSuffix(secretPart) {\n        // Take last 4 characters for display purposes\n        const suffix = secretPart.slice(-4);\n        return await (0,_lib_encryption__WEBPACK_IMPORTED_MODULE_0__.encrypt)(suffix);\n    }\n    /**\n   * Decrypts the suffix for display\n   * @param encryptedSuffix The encrypted suffix from database\n   * @returns Decrypted suffix for display\n   */ static async decryptSuffix(encryptedSuffix) {\n        try {\n            return await (0,_lib_encryption__WEBPACK_IMPORTED_MODULE_0__.decrypt)(encryptedSuffix);\n        } catch (error) {\n            console.error('Failed to decrypt API key suffix:', error);\n            // Return a placeholder that looks like the last 4 chars\n            return 'xxxx';\n        }\n    }\n    /**\n   * Creates a masked version of the API key for display\n   * @param prefix The key prefix\n   * @param encryptedSuffix The encrypted suffix\n   * @returns Masked key for display (e.g., \"rk_live_abc12345_****xyz\")\n   */ static async createMaskedKey(prefix, encryptedSuffix) {\n        const suffix = await this.decryptSuffix(encryptedSuffix);\n        // Show 4 chars at the end, mask the rest (32 - 4 = 28 chars to mask)\n        const maskedLength = this.SECRET_PART_LENGTH - 4;\n        return `${prefix}_${'*'.repeat(maskedLength)}${suffix}`;\n    }\n    /**\n   * Validates subscription tier limits for API key generation\n   * @param subscriptionTier User's subscription tier\n   * @param currentKeyCount Current number of API keys for the user\n   * @returns Object indicating if generation is allowed and any limits\n   */ static validateSubscriptionLimits(subscriptionTier, currentKeyCount) {\n        // User-generated API keys limits (separate from API keys per config)\n        // These are total API keys a user can generate across all their configs\n        const limits = {\n            free: 3,\n            starter: 50,\n            professional: 999999,\n            enterprise: 999999 // Enterprise users get unlimited user-generated API keys\n        };\n        const limit = limits[subscriptionTier] || limits.free;\n        if (currentKeyCount >= limit) {\n            return {\n                allowed: false,\n                limit,\n                message: `You have reached the maximum number of user-generated API keys (${limit}) for your ${subscriptionTier} plan.`\n            };\n        }\n        return {\n            allowed: true,\n            limit\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/userApiKeys/apiKeyGenerator.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser-api-keys%2F%5BkeyId%5D%2Froute&page=%2Fapi%2Fuser-api-keys%2F%5BkeyId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser-api-keys%2F%5BkeyId%5D%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();