"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0bd40a5bb0a2\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjBiZDQwYTViYjBhMlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/GlobalSearch.tsx":
/*!*****************************************!*\
  !*** ./src/components/GlobalSearch.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalSearch: () => (/* binding */ GlobalSearch)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=MagnifyingGlassIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=MagnifyingGlassIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BeakerIcon_ChartBarIcon_CogIcon_DocumentTextIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BeakerIcon,ChartBarIcon,CogIcon,DocumentTextIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BeakerIcon_ChartBarIcon_CogIcon_DocumentTextIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BeakerIcon,ChartBarIcon,CogIcon,DocumentTextIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BeakerIcon_ChartBarIcon_CogIcon_DocumentTextIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BeakerIcon,ChartBarIcon,CogIcon,DocumentTextIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BeakerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BeakerIcon_ChartBarIcon_CogIcon_DocumentTextIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BeakerIcon,ChartBarIcon,CogIcon,DocumentTextIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BeakerIcon_ChartBarIcon_CogIcon_DocumentTextIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BeakerIcon,ChartBarIcon,CogIcon,DocumentTextIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BeakerIcon_ChartBarIcon_CogIcon_DocumentTextIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BeakerIcon,ChartBarIcon,CogIcon,DocumentTextIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* __next_internal_client_entry_do_not_use__ GlobalSearch auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction GlobalSearch(param) {\n    let { isOpen, onClose } = param;\n    _s();\n    const [query, setQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedIndex, setSelectedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Static page results\n    const staticPages = [\n        {\n            id: 'dashboard',\n            title: 'Dashboard',\n            subtitle: 'Overview & analytics',\n            type: 'page',\n            href: '/dashboard',\n            icon: _barrel_optimize_names_BeakerIcon_ChartBarIcon_CogIcon_DocumentTextIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n        },\n        {\n            id: 'my-models',\n            title: 'My Models',\n            subtitle: 'API key management',\n            type: 'page',\n            href: '/my-models',\n            icon: _barrel_optimize_names_BeakerIcon_ChartBarIcon_CogIcon_DocumentTextIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            id: 'playground',\n            title: 'Playground',\n            subtitle: 'Test your models',\n            type: 'page',\n            href: '/playground',\n            icon: _barrel_optimize_names_BeakerIcon_ChartBarIcon_CogIcon_DocumentTextIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            id: 'routing-setup',\n            title: 'Routing Setup',\n            subtitle: 'Configure routing',\n            type: 'page',\n            href: '/routing-setup',\n            icon: _barrel_optimize_names_BeakerIcon_ChartBarIcon_CogIcon_DocumentTextIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            id: 'logs',\n            title: 'Logs',\n            subtitle: 'Request history',\n            type: 'page',\n            href: '/logs',\n            icon: _barrel_optimize_names_BeakerIcon_ChartBarIcon_CogIcon_DocumentTextIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        }\n    ];\n    // Focus input when opened\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GlobalSearch.useEffect\": ()=>{\n            if (isOpen && inputRef.current) {\n                inputRef.current.focus();\n            }\n        }\n    }[\"GlobalSearch.useEffect\"], [\n        isOpen\n    ]);\n    // Search function\n    const performSearch = async (searchQuery)=>{\n        if (!searchQuery.trim()) {\n            setResults([]);\n            return;\n        }\n        setLoading(true);\n        const allResults = [];\n        try {\n            // Search static pages\n            const pageResults = staticPages.filter((page)=>page.title.toLowerCase().includes(searchQuery.toLowerCase()) || page.subtitle.toLowerCase().includes(searchQuery.toLowerCase()));\n            allResults.push(...pageResults);\n            // Search API configurations\n            let configResults = [];\n            try {\n                const configResponse = await fetch('/api/custom-configs');\n                if (configResponse.ok) {\n                    const configData = await configResponse.json();\n                    const configs = configData.configs || configData || [];\n                    if (Array.isArray(configs)) {\n                        configResults = configs.filter((config)=>{\n                            var _config_name;\n                            return config === null || config === void 0 ? void 0 : (_config_name = config.name) === null || _config_name === void 0 ? void 0 : _config_name.toLowerCase().includes(searchQuery.toLowerCase());\n                        }).map((config)=>{\n                            var _config_api_keys;\n                            return {\n                                id: \"config-\".concat(config.id),\n                                title: config.name,\n                                subtitle: \"Configuration • \".concat(config.routing_strategy || 'Unknown'),\n                                type: 'config',\n                                href: \"/my-models/\".concat(config.id),\n                                icon: _barrel_optimize_names_BeakerIcon_ChartBarIcon_CogIcon_DocumentTextIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                                metadata: \"\".concat(((_config_api_keys = config.api_keys) === null || _config_api_keys === void 0 ? void 0 : _config_api_keys.length) || 0, \" keys\")\n                            };\n                        });\n                    }\n                }\n            } catch (error) {\n                console.error('Error searching configurations:', error);\n            }\n            // Search user-generated API keys\n            let userKeyResults = [];\n            try {\n                const userKeysResponse = await fetch('/api/user-api-keys');\n                if (userKeysResponse.ok) {\n                    const userKeysData = await userKeysResponse.json();\n                    const apiKeys = userKeysData.api_keys || userKeysData || [];\n                    if (Array.isArray(apiKeys)) {\n                        userKeyResults = apiKeys.filter((key)=>{\n                            var _key_key_name;\n                            return key === null || key === void 0 ? void 0 : (_key_key_name = key.key_name) === null || _key_key_name === void 0 ? void 0 : _key_key_name.toLowerCase().includes(searchQuery.toLowerCase());\n                        }).map((key)=>({\n                                id: \"user-key-\".concat(key.id),\n                                title: key.key_name,\n                                subtitle: \"User API Key • \".concat(key.status || 'Unknown'),\n                                type: 'user-api-key',\n                                href: \"/my-models/\".concat(key.custom_api_config_id, \"?tab=user-api-keys\"),\n                                icon: _barrel_optimize_names_BeakerIcon_ChartBarIcon_CogIcon_DocumentTextIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                                metadata: \"\".concat(key.total_requests || 0, \" requests\")\n                            }));\n                    }\n                }\n            } catch (error) {\n                console.error('Error searching user API keys:', error);\n            }\n            // Add configuration and user key results to the main results array\n            allResults.push(...configResults, ...userKeyResults);\n            // Sort results by relevance (exact matches first, then partial matches)\n            const sortedResults = allResults.sort((a, b)=>{\n                const aExact = a.title.toLowerCase() === searchQuery.toLowerCase();\n                const bExact = b.title.toLowerCase() === searchQuery.toLowerCase();\n                if (aExact && !bExact) return -1;\n                if (!aExact && bExact) return 1;\n                return 0;\n            });\n            setResults(sortedResults.slice(0, 10)); // Limit to 10 results\n            setSelectedIndex(0);\n        } catch (error) {\n            console.error('Search error:', error);\n            setResults([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Debounced search\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GlobalSearch.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"GlobalSearch.useEffect.timer\": ()=>{\n                    performSearch(query);\n                }\n            }[\"GlobalSearch.useEffect.timer\"], 300);\n            return ({\n                \"GlobalSearch.useEffect\": ()=>clearTimeout(timer)\n            })[\"GlobalSearch.useEffect\"];\n        }\n    }[\"GlobalSearch.useEffect\"], [\n        query\n    ]);\n    // Handle keyboard navigation\n    const handleKeyDown = (e)=>{\n        switch(e.key){\n            case 'ArrowDown':\n                e.preventDefault();\n                setSelectedIndex((prev)=>Math.min(prev + 1, results.length - 1));\n                break;\n            case 'ArrowUp':\n                e.preventDefault();\n                setSelectedIndex((prev)=>Math.max(prev - 1, 0));\n                break;\n            case 'Enter':\n                e.preventDefault();\n                if (results[selectedIndex]) {\n                    handleResultClick(results[selectedIndex]);\n                }\n                break;\n            case 'Escape':\n                e.preventDefault();\n                onClose();\n                break;\n        }\n    };\n    const handleResultClick = (result)=>{\n        router.push(result.href);\n        onClose();\n        setQuery('');\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/20 backdrop-blur-sm z-50\",\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-20 left-1/2 transform -translate-x-1/2 w-full max-w-2xl mx-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-2xl border border-gray-200 overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center px-4 py-3 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-5 w-5 text-gray-400 mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    ref: inputRef,\n                                    type: \"text\",\n                                    placeholder: \"Search configurations, API keys, pages...\",\n                                    value: query,\n                                    onChange: (e)=>setQuery(e.target.value),\n                                    onKeyDown: handleKeyDown,\n                                    className: \"flex-1 text-gray-900 placeholder-gray-500 bg-transparent border-none outline-none text-sm\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"p-1 hover:bg-gray-100 rounded-lg transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-h-96 overflow-y-auto\",\n                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-8 text-center text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500 mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-sm\",\n                                        children: \"Searching...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 15\n                            }, this) : results.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"py-2\",\n                                children: results.map((result, index)=>{\n                                    const Icon = result.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleResultClick(result),\n                                        className: \"w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors flex items-center space-x-3 \".concat(index === selectedIndex ? 'bg-orange-50 border-r-2 border-orange-500' : ''),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"h-5 w-5 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-900 truncate\",\n                                                        children: result.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 truncate\",\n                                                        children: result.subtitle\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 23\n                                            }, this),\n                                            result.metadata && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: result.metadata\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 27\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, result.id, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 21\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 15\n                            }, this) : query.trim() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-8 text-center text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-8 w-8 mx-auto text-gray-300 mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: [\n                                            'No results found for \"',\n                                            query,\n                                            '\"'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-400 mt-1\",\n                                        children: \"Try searching for configurations, API keys, or page names\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-8 text-center text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-8 w-8 mx-auto text-gray-300 mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: \"Start typing to search...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-400 mt-1\",\n                                        children: \"Search across configurations, API keys, and pages\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 bg-gray-50 border-t border-gray-200 text-xs text-gray-500 flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"↑↓ Navigate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"↵ Select\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Esc Close\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        results.length,\n                                        \" results\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(GlobalSearch, \"SfO9/tirtWw2ms8PdYF/Xa+koQc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = GlobalSearch;\nvar _c;\n$RefreshReg$(_c, \"GlobalSearch\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/GlobalSearch.tsx\n"));

/***/ })

});