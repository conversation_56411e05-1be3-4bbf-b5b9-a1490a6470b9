"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/[configId]/page",{

/***/ "(app-pages-browser)/./src/components/UserApiKeys/ApiKeyManager.tsx":
/*!******************************************************!*\
  !*** ./src/components/UserApiKeys/ApiKeyManager.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiKeyManager: () => (/* binding */ ApiKeyManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _ApiKeyCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ApiKeyCard */ \"(app-pages-browser)/./src/components/UserApiKeys/ApiKeyCard.tsx\");\n/* harmony import */ var _CreateApiKeyDialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CreateApiKeyDialog */ \"(app-pages-browser)/./src/components/UserApiKeys/CreateApiKeyDialog.tsx\");\n/* harmony import */ var _EditApiKeyDialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./EditApiKeyDialog */ \"(app-pages-browser)/./src/components/UserApiKeys/EditApiKeyDialog.tsx\");\n/* harmony import */ var _ApiKeyUsageDialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ApiKeyUsageDialog */ \"(app-pages-browser)/./src/components/UserApiKeys/ApiKeyUsageDialog.tsx\");\n/* harmony import */ var _components_TierEnforcement__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/TierEnforcement */ \"(app-pages-browser)/./src/components/TierEnforcement/index.ts\");\n/* harmony import */ var _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/useConfirmation */ \"(app-pages-browser)/./src/hooks/useConfirmation.ts\");\n/* __next_internal_client_entry_do_not_use__ ApiKeyManager auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction ApiKeyManager(param) {\n    let { configId, configName } = param;\n    _s();\n    const [apiKeys, setApiKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [creating, setCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCreateDialog, setShowCreateDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [subscriptionInfo, setSubscriptionInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const confirmation = (0,_hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_10__.useConfirmation)();\n    // Fetch API keys for this configuration\n    const fetchApiKeys = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/user-api-keys?config_id=\".concat(configId));\n            if (!response.ok) {\n                throw new Error('Failed to fetch API keys');\n            }\n            const data = await response.json();\n            setApiKeys(data.api_keys || []);\n        } catch (error) {\n            console.error('Error fetching API keys:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error('Failed to load API keys');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Fetch subscription info\n    const fetchSubscriptionInfo = async (currentKeyCount)=>{\n        try {\n            // Get the tier from the user's active subscription\n            const tierResponse = await fetch('/api/user/subscription-tier');\n            const tierData = tierResponse.ok ? await tierResponse.json() : null;\n            const tier = (tierData === null || tierData === void 0 ? void 0 : tierData.tier) || 'starter';\n            const limits = {\n                free: 3,\n                starter: 50,\n                professional: 999999,\n                enterprise: 999999\n            };\n            // Use provided count or current apiKeys length\n            const keyCount = currentKeyCount !== undefined ? currentKeyCount : apiKeys.length;\n            setSubscriptionInfo({\n                tier,\n                keyLimit: limits[tier] || limits.free,\n                currentCount: keyCount\n            });\n        } catch (error) {\n            console.error('Error fetching subscription info:', error);\n            // Fallback to free tier\n            const keyCount = currentKeyCount !== undefined ? currentKeyCount : apiKeys.length;\n            setSubscriptionInfo({\n                tier: 'free',\n                keyLimit: 3,\n                currentCount: keyCount\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ApiKeyManager.useEffect\": ()=>{\n            fetchApiKeys();\n        }\n    }[\"ApiKeyManager.useEffect\"], [\n        configId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ApiKeyManager.useEffect\": ()=>{\n            if (apiKeys.length >= 0) {\n                fetchSubscriptionInfo();\n            }\n        }\n    }[\"ApiKeyManager.useEffect\"], [\n        apiKeys\n    ]);\n    const handleCreateApiKey = async (keyData)=>{\n        try {\n            setCreating(true);\n            const response = await fetch('/api/user-api-keys', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...keyData,\n                    custom_api_config_id: configId\n                })\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                throw new Error(error.error || 'Failed to create API key');\n            }\n            const newApiKey = await response.json();\n            // Show the full API key in a special dialog since it's only shown once\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success('API key created successfully!');\n            // Optimistically add the new key to the list immediately\n            const optimisticKey = {\n                ...newApiKey,\n                // Add any missing fields that might be needed for display\n                custom_api_configs: {\n                    id: configId,\n                    name: configName\n                }\n            };\n            setApiKeys((prevKeys)=>{\n                const newKeys = [\n                    optimisticKey,\n                    ...prevKeys\n                ];\n                // Immediately update subscription info with new count\n                fetchSubscriptionInfo(newKeys.length);\n                return newKeys;\n            });\n            // Also refresh to ensure we have the latest data and correct counts\n            await fetchApiKeys();\n            return newApiKey;\n        } catch (error) {\n            console.error('Error creating API key:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(error.message || 'Failed to create API key');\n            throw error;\n        } finally{\n            setCreating(false);\n        }\n    };\n    const handleRevokeApiKey = async (keyId)=>{\n        const apiKey = apiKeys.find((key)=>key.id === keyId);\n        const keyName = (apiKey === null || apiKey === void 0 ? void 0 : apiKey.key_name) || 'this API key';\n        confirmation.showConfirmation({\n            title: 'Revoke API Key',\n            message: 'Are you sure you want to revoke \"'.concat(keyName, '\"? This action cannot be undone and will immediately disable the key.'),\n            confirmText: 'Revoke Key',\n            cancelText: 'Cancel',\n            type: 'danger'\n        }, async ()=>{\n            try {\n                // Optimistically update the key status to revoked instead of removing it\n                const previousKeys = [\n                    ...apiKeys\n                ];\n                setApiKeys((prevKeys)=>prevKeys.map((key)=>key.id === keyId ? {\n                            ...key,\n                            status: 'revoked'\n                        } : key));\n                const response = await fetch(\"/api/user-api-keys/\".concat(keyId), {\n                    method: 'DELETE'\n                });\n                if (!response.ok) {\n                    // Revert the optimistic update on error\n                    setApiKeys(previousKeys);\n                    const error = await response.json();\n                    throw new Error(error.error || 'Failed to revoke API key');\n                }\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success('API key revoked successfully');\n                // Refresh to ensure we have the latest data\n                await fetchApiKeys();\n            } catch (error) {\n                console.error('Error revoking API key:', error);\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(error.message || 'Failed to revoke API key');\n                throw error; // Re-throw to let the confirmation modal handle the error state\n            }\n        });\n    };\n    const handleCreateDialogClose = (open)=>{\n        setShowCreateDialog(open);\n    // Note: We no longer need to refresh here since we refresh immediately after creation\n    };\n    const canCreateMoreKeys = subscriptionInfo ? subscriptionInfo.currentCount < subscriptionInfo.keyLimit : true;\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"flex items-center justify-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"h-6 w-6 animate-spin mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, this),\n                    \"Loading API keys...\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 215,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n            lineNumber: 214,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"API Keys\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: [\n                                    \"Generate API keys for programmatic access to \",\n                                    configName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this),\n                    canCreateMoreKeys ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: ()=>setShowCreateDialog(true),\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 13\n                            }, this),\n                            \"Create API Key\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-end gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                disabled: true,\n                                className: \"flex items-center gap-2 opacity-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Create API Key\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-orange-600 font-medium\",\n                                children: (subscriptionInfo === null || subscriptionInfo === void 0 ? void 0 : subscriptionInfo.tier) === 'free' ? 'Upgrade to Starter plan for more API keys' : 'API key limit reached - upgrade for unlimited keys'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, this),\n            subscriptionInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg border border-gray-200 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_9__.LimitIndicator, {\n                    current: subscriptionInfo.currentCount,\n                    limit: subscriptionInfo.keyLimit,\n                    label: \"User-Generated API Keys\",\n                    tier: subscriptionInfo.tier,\n                    showUpgradeHint: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 265,\n                columnNumber: 9\n            }, this),\n            apiKeys.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"text-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"h-12 w-12 mx-auto text-gray-400 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-2\",\n                            children: \"No API Keys\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Create your first API key to start using the RouKey API programmatically.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 13\n                        }, this),\n                        canCreateMoreKeys ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: ()=>setShowCreateDialog(true),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 17\n                                }, this),\n                                \"Create Your First API Key\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    disabled: true,\n                                    className: \"opacity-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Create Your First API Key\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-orange-600 font-medium\",\n                                    children: (subscriptionInfo === null || subscriptionInfo === void 0 ? void 0 : subscriptionInfo.tier) === 'free' ? 'Upgrade to Starter plan to create API keys' : 'API key limit reached - upgrade for unlimited keys'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                    lineNumber: 279,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 278,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4\",\n                children: apiKeys.map((apiKey)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ApiKeyCard__WEBPACK_IMPORTED_MODULE_5__.ApiKeyCard, {\n                        apiKey: apiKey,\n                        onEdit: (key)=>{\n                            setSelectedApiKey(key);\n                            setShowEditDialog(true);\n                        },\n                        onRevoke: handleRevokeApiKey,\n                        onViewUsage: handleViewUsage\n                    }, apiKey.id, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 309,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateApiKeyDialog__WEBPACK_IMPORTED_MODULE_6__.CreateApiKeyDialog, {\n                open: showCreateDialog,\n                onOpenChange: handleCreateDialogClose,\n                onCreateApiKey: handleCreateApiKey,\n                configName: configName,\n                creating: creating,\n                subscriptionTier: (subscriptionInfo === null || subscriptionInfo === void 0 ? void 0 : subscriptionInfo.tier) || 'starter'\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 326,\n                columnNumber: 7\n            }, this),\n            selectedApiKey && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EditApiKeyDialog__WEBPACK_IMPORTED_MODULE_7__.EditApiKeyDialog, {\n                        open: showEditDialog,\n                        onOpenChange: setShowEditDialog,\n                        apiKey: selectedApiKey,\n                        onUpdateApiKey: (updates)=>handleEditApiKey(selectedApiKey.id, updates)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ApiKeyUsageDialog__WEBPACK_IMPORTED_MODULE_8__.ApiKeyUsageDialog, {\n                        open: showUsageDialog,\n                        onOpenChange: setShowUsageDialog,\n                        apiKey: selectedApiKey\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n        lineNumber: 224,\n        columnNumber: 5\n    }, this);\n}\n_s(ApiKeyManager, \"wzx+xNoPucKu2oL+bFaGPsR3hPI=\", false, function() {\n    return [\n        _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_10__.useConfirmation\n    ];\n});\n_c = ApiKeyManager;\nvar _c;\n$RefreshReg$(_c, \"ApiKeyManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/UserApiKeys/ApiKeyManager.tsx\n"));

/***/ })

});