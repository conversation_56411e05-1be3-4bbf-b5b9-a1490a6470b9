'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/badge';
import { Plus, Key, AlertCircle, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';
import { ApiKeyCard } from './ApiKeyCard';
import { CreateApiKeyDialog } from './CreateApiKeyDialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { LimitIndicator } from '@/components/TierEnforcement';
import ConfirmationModal from '@/components/ui/ConfirmationModal';
import { useConfirmation } from '@/hooks/useConfirmation';

interface ApiKeyManagerProps {
  configId: string;
  configName: string;
}

export function ApiKeyManager({ configId, configName }: ApiKeyManagerProps) {
  const [apiKeys, setApiKeys] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [subscriptionInfo, setSubscriptionInfo] = useState<{
    tier: string;
    keyLimit: number;
    currentCount: number;
  } | null>(null);

  const confirmation = useConfirmation();

  // Fetch API keys for this configuration
  const fetchApiKeys = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/user-api-keys?config_id=${configId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch API keys');
      }

      const data = await response.json();
      setApiKeys(data.api_keys || []);
    } catch (error) {
      console.error('Error fetching API keys:', error);
      toast.error('Failed to load API keys');
    } finally {
      setLoading(false);
    }
  };

  // Fetch subscription info
  const fetchSubscriptionInfo = async (currentKeyCount?: number) => {
    try {
      // Get the tier from the user's active subscription
      const tierResponse = await fetch('/api/user/subscription-tier');
      const tierData = tierResponse.ok ? await tierResponse.json() : null;
      const tier = tierData?.tier || 'starter';

      const limits = {
        free: 3,
        starter: 50,
        professional: 999999,
        enterprise: 999999
      };

      // Use provided count or current apiKeys length
      const keyCount = currentKeyCount !== undefined ? currentKeyCount : apiKeys.length;

      setSubscriptionInfo({
        tier,
        keyLimit: limits[tier as keyof typeof limits] || limits.free,
        currentCount: keyCount
      });
    } catch (error) {
      console.error('Error fetching subscription info:', error);
      // Fallback to free tier
      const keyCount = currentKeyCount !== undefined ? currentKeyCount : apiKeys.length;
      setSubscriptionInfo({
        tier: 'free',
        keyLimit: 3,
        currentCount: keyCount
      });
    }
  };

  useEffect(() => {
    fetchApiKeys();
  }, [configId]);

  useEffect(() => {
    if (apiKeys.length >= 0) {
      fetchSubscriptionInfo();
    }
  }, [apiKeys]);

  const handleCreateApiKey = async (keyData: any) => {
    try {
      setCreating(true);
      const response = await fetch('/api/user-api-keys', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...keyData,
          custom_api_config_id: configId,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create API key');
      }

      const newApiKey = await response.json();

      // Show the full API key in a special dialog since it's only shown once
      toast.success('API key created successfully!');

      // Optimistically add the new key to the list immediately
      const optimisticKey = {
        ...newApiKey,
        // Add any missing fields that might be needed for display
        custom_api_configs: {
          id: configId,
          name: configName
        }
      };
      setApiKeys(prevKeys => {
        const newKeys = [optimisticKey, ...prevKeys];
        // Immediately update subscription info with new count
        fetchSubscriptionInfo(newKeys.length);
        return newKeys;
      });

      // Also refresh to ensure we have the latest data and correct counts
      await fetchApiKeys();

      return newApiKey;
    } catch (error: any) {
      console.error('Error creating API key:', error);
      toast.error(error.message || 'Failed to create API key');
      throw error;
    } finally {
      setCreating(false);
    }
  };



  const handleRevokeApiKey = async (keyId: string) => {
    const apiKey = apiKeys.find(key => key.id === keyId);
    const keyName = apiKey?.key_name || 'this API key';

    confirmation.showConfirmation(
      {
        title: 'Revoke API Key',
        message: `Are you sure you want to revoke "${keyName}"? This action cannot be undone and will immediately disable the key.`,
        confirmText: 'Revoke Key',
        cancelText: 'Cancel',
        type: 'danger'
      },
      async () => {
        try {
          const response = await fetch(`/api/user-api-keys/${keyId}`, {
            method: 'DELETE',
          });

          if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || 'Failed to revoke API key');
          }

          // Update the key status to revoked immediately after successful API call
          setApiKeys(prevKeys => {
            const updatedKeys = prevKeys.map(key =>
              key.id === keyId ? { ...key, status: 'revoked' } : key
            );
            console.log('Updated API keys after revoke:', updatedKeys.find(k => k.id === keyId));
            return updatedKeys;
          });

          toast.success('API key revoked successfully');
        } catch (error: any) {
          console.error('Error revoking API key:', error);
          toast.error(error.message || 'Failed to revoke API key');
          throw error; // Re-throw to let the confirmation modal handle the error state
        }
      }
    );
  };



  const handleCreateDialogClose = (open: boolean) => {
    setShowCreateDialog(open);
    // Note: We no longer need to refresh here since we refresh immediately after creation
  };

  const canCreateMoreKeys = subscriptionInfo 
    ? subscriptionInfo.currentCount < subscriptionInfo.keyLimit 
    : true;

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <RefreshCw className="h-6 w-6 animate-spin mr-2" />
          Loading API keys...
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Key className="h-6 w-6" />
            API Keys
          </h2>
          <p className="text-gray-600 mt-1">
            Generate API keys for programmatic access to {configName}
          </p>
        </div>
        {canCreateMoreKeys ? (
          <Button
            onClick={() => setShowCreateDialog(true)}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Create API Key
          </Button>
        ) : (
          <div className="flex flex-col items-end gap-2">
            <Button
              disabled
              className="flex items-center gap-2 opacity-50"
            >
              <Plus className="h-4 w-4" />
              Create API Key
            </Button>
            <p className="text-xs text-orange-600 font-medium">
              {subscriptionInfo?.tier === 'free'
                ? 'Upgrade to Starter plan for more API keys'
                : 'API key limit reached - upgrade for unlimited keys'
              }
            </p>
          </div>
        )}
      </div>

      {/* API Key Usage Limits */}
      {subscriptionInfo && (
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <LimitIndicator
            current={subscriptionInfo.currentCount}
            limit={subscriptionInfo.keyLimit}
            label="User-Generated API Keys"
            tier={subscriptionInfo.tier as any}
            showUpgradeHint={true}
          />
        </div>
      )}

      {/* API Keys List */}
      {apiKeys.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <Key className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-semibold mb-2">No API Keys</h3>
            <p className="text-gray-600 mb-4">
              Create your first API key to start using the RouKey API programmatically.
            </p>
            {canCreateMoreKeys ? (
              <Button
                onClick={() => setShowCreateDialog(true)}
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Your First API Key
              </Button>
            ) : (
              <div className="flex flex-col items-center gap-2">
                <Button disabled className="opacity-50">
                  <Plus className="h-4 w-4 mr-2" />
                  Create Your First API Key
                </Button>
                <p className="text-xs text-orange-600 font-medium">
                  {subscriptionInfo?.tier === 'free'
                    ? 'Upgrade to Starter plan to create API keys'
                    : 'API key limit reached - upgrade for unlimited keys'
                  }
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {apiKeys.map((apiKey) => (
            <ApiKeyCard
              key={apiKey.id}
              apiKey={apiKey}
              onRevoke={handleRevokeApiKey}
            />
          ))}
        </div>
      )}

      {/* Dialogs */}
      <CreateApiKeyDialog
        open={showCreateDialog}
        onOpenChange={handleCreateDialogClose}
        onCreateApiKey={handleCreateApiKey}
        configName={configName}
        creating={creating}
        subscriptionTier={subscriptionInfo?.tier || 'starter'}
      />

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={confirmation.isOpen}
        onClose={confirmation.hideConfirmation}
        onConfirm={confirmation.onConfirm}
        title={confirmation.title}
        message={confirmation.message}
        confirmText={confirmation.confirmText}
        cancelText={confirmation.cancelText}
        type={confirmation.type}
        isLoading={confirmation.isLoading}
      />
    </div>
  );
}
