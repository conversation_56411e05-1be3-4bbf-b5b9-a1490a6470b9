/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/keys/[apiKeyId]/roles/route";
exports.ids = ["app/api/keys/[apiKeyId]/roles/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkeys%2F%5BapiKeyId%5D%2Froles%2Froute&page=%2Fapi%2Fkeys%2F%5BapiKeyId%5D%2Froles%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkeys%2F%5BapiKeyId%5D%2Froles%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkeys%2F%5BapiKeyId%5D%2Froles%2Froute&page=%2Fapi%2Fkeys%2F%5BapiKeyId%5D%2Froles%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkeys%2F%5BapiKeyId%5D%2Froles%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_keys_apiKeyId_roles_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/keys/[apiKeyId]/roles/route.ts */ \"(rsc)/./src/app/api/keys/[apiKeyId]/roles/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/keys/[apiKeyId]/roles/route\",\n        pathname: \"/api/keys/[apiKeyId]/roles\",\n        filename: \"route\",\n        bundlePath: \"app/api/keys/[apiKeyId]/roles/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\keys\\\\[apiKeyId]\\\\roles\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_keys_apiKeyId_roles_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZrZXlzJTJGJTVCYXBpS2V5SWQlNUQlMkZyb2xlcyUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGa2V5cyUyRiU1QmFwaUtleUlkJTVEJTJGcm9sZXMlMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZrZXlzJTJGJTVCYXBpS2V5SWQlNUQlMkZyb2xlcyUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDUm9LZXklMjBBcHAlNUNyb2tleS1hcHAlNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNSb0tleSUyMEFwcCU1Q3Jva2V5LWFwcCZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDcUI7QUFDMEI7QUFDdkc7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHlHQUFtQjtBQUMzQztBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxzREFBc0Q7QUFDOUQ7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDMEY7O0FBRTFGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkM6XFxcXFJvS2V5IEFwcFxcXFxyb2tleS1hcHBcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxca2V5c1xcXFxbYXBpS2V5SWRdXFxcXHJvbGVzXFxcXHJvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9rZXlzL1thcGlLZXlJZF0vcm9sZXMvcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9rZXlzL1thcGlLZXlJZF0vcm9sZXNcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL2tleXMvW2FwaUtleUlkXS9yb2xlcy9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIkM6XFxcXFJvS2V5IEFwcFxcXFxyb2tleS1hcHBcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxca2V5c1xcXFxbYXBpS2V5SWRdXFxcXHJvbGVzXFxcXHJvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgd29ya0FzeW5jU3RvcmFnZSxcbiAgICAgICAgd29ya1VuaXRBc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkeys%2F%5BapiKeyId%5D%2Froles%2Froute&page=%2Fapi%2Fkeys%2F%5BapiKeyId%5D%2Froles%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkeys%2F%5BapiKeyId%5D%2Froles%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/keys/[apiKeyId]/roles/route.ts":
/*!****************************************************!*\
  !*** ./src/app/api/keys/[apiKeyId]/roles/route.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _config_roles__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/config/roles */ \"(rsc)/./src/config/roles.ts\");\n/* harmony import */ var _lib_stripe_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/stripe-client */ \"(rsc)/./src/lib/stripe-client.ts\");\n\n\n // For validation\n\n// GET /api/keys/:apiKeyId/roles\n// Lists all assigned roles for a specific API key.\nasync function GET(request, { params }) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    const { apiKeyId } = await params;\n    if (!apiKeyId) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'API Key ID is required'\n        }, {\n            status: 400\n        });\n    }\n    // TODO: Milestone 13: Auth check - user owns the API key or its parent custom_config.\n    // For now, we also need the user context if we were to enrich with their custom roles by name.\n    // const { data: { user: authUser } } = await supabase.auth.getUser(); \n    try {\n        const { data, error } = await supabase.from('api_key_role_assignments').select('role_name, created_at') // Could select more if needed, like the full Role object by joining or mapping\n        .eq('api_key_id', apiKeyId);\n        if (error) {\n            console.error('Supabase error fetching role assignments:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch role assignments',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        // Enrich data: Attempt to get details for predefined roles.\n        // For custom roles, we'd need to fetch them based on the user who owns the config to get name/description.\n        // This GET endpoint is primarily for listing assigned role_names; the client can fetch full details if needed.\n        const enrichedData = data.map((assignment)=>{\n            const predefinedRoleDetails = (0,_config_roles__WEBPACK_IMPORTED_MODULE_2__.getRoleById)(assignment.role_name);\n            // If it's not predefined, it might be a custom role. The client has the custom roles list.\n            return {\n                ...assignment,\n                role_details: predefinedRoleDetails || {\n                    id: assignment.role_name,\n                    name: assignment.role_name,\n                    description: 'Custom role (details managed globally)'\n                }\n            };\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(enrichedData || [], {\n            status: 200\n        });\n    } catch (e) {\n        console.error('Error in GET /api/keys/:apiKeyId/roles:', e);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected error occurred',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/keys/:apiKeyId/roles\n// Assigns a new role (predefined or user's global custom) to an API key.\nasync function POST(request, { params }) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    const { apiKeyId } = await params;\n    // Get authenticated user from session\n    const { data: { session }, error: sessionError } = await supabase.auth.getSession();\n    if (sessionError || !session?.user) {\n        console.error('Authentication error in POST role assignment:', sessionError);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Unauthorized: You must be logged in to assign roles.'\n        }, {\n            status: 401\n        });\n    }\n    const authenticatedUserId = session.user.id;\n    if (!apiKeyId) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'API Key ID is required'\n        }, {\n            status: 400\n        });\n    }\n    try {\n        const { role_name } = await request.json();\n        if (!role_name || typeof role_name !== 'string') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Role name (role_id) is required and must be a string'\n            }, {\n                status: 400\n            });\n        }\n        // Fetch API key details and the user_id of the custom_api_config owner\n        const { data: apiKeyRecord, error: apiKeyFetchError } = await supabase.from('api_keys').select(`\n        custom_api_config_id,\n        custom_api_configs ( user_id )\n      `).eq('id', apiKeyId).single();\n        if (apiKeyFetchError || !apiKeyRecord) {\n            console.error('API Key not found or error fetching details:', apiKeyFetchError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'API Key not found or failed to fetch its details'\n            }, {\n                status: 404\n            });\n        }\n        // Ensure the fetched apiKeyRecord.custom_api_configs is not null and has a user_id\n        // Supabase typing for nested selects can be tricky, so we ensure structure.\n        const configOwnerUserId = apiKeyRecord.custom_api_configs?.user_id;\n        if (!configOwnerUserId) {\n            console.error('Could not determine the owner of the Custom API Configuration for the API Key.');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Could not determine the config owner for the API Key.'\n            }, {\n                status: 500\n            });\n        }\n        // Authorization: Check if the authenticated user owns the config to which this API key belongs.\n        // This is a critical check that should ideally be part of RLS or a reusable middleware.\n        // For now, implementing it directly here.\n        // TEMPORARY: If configOwnerUserId was not found, but it's the placeholder user, this check is effectively bypassed.\n        if (configOwnerUserId && authenticatedUserId !== configOwnerUserId) {\n            console.warn(`User ${authenticatedUserId} attempted to assign role to API key ${apiKeyId} owned by user ${configOwnerUserId}.`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Forbidden. You do not own the configuration this API key belongs to.'\n            }, {\n                status: 403\n            });\n        }\n        // Check user's subscription tier for custom roles access\n        const { data: subscription } = await supabase.from('subscriptions').select('tier').eq('user_id', authenticatedUserId).eq('status', 'active').single();\n        const userTier = subscription?.tier || 'free';\n        // Check if user has access to custom roles feature\n        if (!(0,_lib_stripe_client__WEBPACK_IMPORTED_MODULE_3__.hasFeatureAccess)(userTier, 'custom_roles')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Role assignment is not available on the ${userTier} plan. Please upgrade to assign roles to your API keys.`\n            }, {\n                status: 403\n            });\n        }\n        // Validate if the role_name is valid for this user\n        const isPredefined = _config_roles__WEBPACK_IMPORTED_MODULE_2__.PREDEFINED_ROLES.some((r)=>r.id === role_name);\n        let isUserCustomRole = false;\n        if (!isPredefined) {\n            const { data: customRole, error: customRoleError } = await supabase.from('user_custom_roles').select('id').eq('user_id', authenticatedUserId) // Role must belong to the authenticated user\n            .eq('role_id', role_name) // Match by the string role_id\n            .maybeSingle(); // Use maybeSingle as it might not exist\n            if (customRoleError) {\n                console.error('Error checking for user custom role:', customRoleError);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Error validating role.',\n                    details: customRoleError.message\n                }, {\n                    status: 500\n                });\n            }\n            if (customRole) {\n                isUserCustomRole = true;\n            }\n        }\n        if (!isPredefined && !isUserCustomRole) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Invalid role_name: ${role_name}. Not a predefined role or a custom role you own.`\n            }, {\n                status: 400\n            });\n        }\n        // The custom_api_config_id is already available from apiKeyRecord\n        const { custom_api_config_id } = apiKeyRecord;\n        const { data: assignmentData, error: assignmentError } = await supabase.from('api_key_role_assignments').insert({\n            api_key_id: apiKeyId,\n            custom_api_config_id: custom_api_config_id,\n            role_name: role_name\n        }).select().single();\n        if (assignmentError) {\n            console.error('Supabase error assigning role:', assignmentError);\n            if (assignmentError.code === '23505') {\n                // The unique constraint `unique_api_key_role` on (api_key_id, role_name) should handle this primarily.\n                // The constraint `unique_role_per_custom_config` on (custom_api_config_id, role_name) might be too restrictive if we allow multiple keys in one config to have the same *custom* role, but it makes sense for predefined roles like 'summarizer'.\n                // Given roles are now more flexible, `unique_api_key_role` is the more important one.\n                // If `unique_role_per_custom_config` is still active and causing issues for custom roles, it might need to be re-evaluated or removed for custom roles.\n                if (assignmentError.message.includes('unique_api_key_role')) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'This API key already has this role assigned.',\n                        details: assignmentError.message\n                    }, {\n                        status: 409\n                    });\n                }\n                // This constraint `unique_role_per_custom_config` was designed when roles were simpler.\n                // It means a role_name (e.g., 'translator') can only be assigned to ONE key within a single Custom API Configuration.\n                // This might still be desired behavior for predefined roles to avoid ambiguity.\n                // For custom roles, a user might want to assign their `my_special_role` to multiple keys in the same config.\n                // This needs careful thought. For now, we respect existing constraints.\n                if (assignmentError.message.includes('unique_role_per_custom_config')) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'This role is already assigned to another API key in this Custom Model (config). Check unique_role_per_custom_config constraint.',\n                        details: assignmentError.message\n                    }, {\n                        status: 409\n                    });\n                }\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Failed to assign role: This role may already be assigned in a way that violates a uniqueness constraint.',\n                    details: assignmentError.message,\n                    code: assignmentError.code\n                }, {\n                    status: 409\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to assign role to API key',\n                details: assignmentError.message\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(assignmentData, {\n            status: 201\n        });\n    } catch (e) {\n        console.error('Error in POST /api/keys/:apiKeyId/roles:', e);\n        if (e.name === 'SyntaxError') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid request body: Malformed JSON.'\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected error occurred',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/keys/[apiKeyId]/roles/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/config/roles.ts":
/*!*****************************!*\
  !*** ./src/config/roles.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PREDEFINED_ROLES: () => (/* binding */ PREDEFINED_ROLES),\n/* harmony export */   getRoleById: () => (/* binding */ getRoleById),\n/* harmony export */   getRoleName: () => (/* binding */ getRoleName)\n/* harmony export */ });\nconst PREDEFINED_ROLES = [\n    {\n        id: 'general_chat',\n        name: 'General Chat',\n        description: 'Casual conversation, simple questions, greetings, and basic interactions that do not require specialized expertise.'\n    },\n    {\n        id: 'logic_reasoning',\n        name: 'Logic & Reasoning',\n        description: 'Mathematical calculations, logical puzzles, analytical problem-solving, deductive reasoning, and complex analytical thinking.'\n    },\n    {\n        id: 'writing',\n        name: 'Writing & Content Creation',\n        description: 'Creating written content like stories, articles, books, poems, scripts, marketing copy, blog posts, and creative narratives.'\n    },\n    {\n        id: 'coding_frontend',\n        name: 'Coding - Frontend',\n        description: 'Building user interfaces with HTML, CSS, JavaScript, React, Vue, Angular, web design, and client-side development.'\n    },\n    {\n        id: 'coding_backend',\n        name: 'Coding - Backend',\n        description: 'Programming server-side applications, APIs, databases, Python scripts, Node.js, Java, backend logic, and system architecture.'\n    },\n    {\n        id: 'research_synthesis',\n        name: 'Research & Synthesis',\n        description: 'Gathering information from sources, conducting research, analyzing data, and synthesizing findings into comprehensive reports.'\n    },\n    {\n        id: 'summarization_briefing',\n        name: 'Summarization & Briefing',\n        description: 'Condensing lengthy documents, extracting key points, creating executive summaries, and briefing complex information.'\n    },\n    {\n        id: 'translation_localization',\n        name: 'Translation & Localization',\n        description: 'Converting text between different languages, linguistic translation, cultural adaptation, and multilingual communication.'\n    },\n    {\n        id: 'data_extraction_structuring',\n        name: 'Data Extraction & Structuring',\n        description: 'Extracting specific information from unstructured text, organizing data into tables, lists, JSON, CSV, and structured formats.'\n    },\n    {\n        id: 'brainstorming_ideation',\n        name: 'Brainstorming & Ideation',\n        description: 'Generating creative ideas, innovative concepts, brainstorming solutions, exploring possibilities, and creative thinking sessions.'\n    },\n    {\n        id: 'education_tutoring',\n        name: 'Education & Tutoring',\n        description: 'Teaching concepts, explaining topics step-by-step, creating educational materials, tutoring, and providing learning guidance.'\n    },\n    {\n        id: 'image_generation',\n        name: 'Image Generation',\n        description: 'Creating visual images, artwork, graphics, and illustrations from textual descriptions using AI image generation models.'\n    },\n    {\n        id: 'audio_transcription',\n        name: 'Audio Transcription',\n        description: 'Converting speech from audio files into written text, transcribing recordings, and speech-to-text processing.'\n    },\n    {\n        id: 'data_extractor',\n        name: 'Data Extractor',\n        description: 'Extracting specific data from web pages, scraping content, and gathering information from websites.'\n    },\n    {\n        id: 'form_filler',\n        name: 'Form Filler',\n        description: 'Filling out web forms, submitting data, and handling form-based interactions on websites.'\n    },\n    {\n        id: 'verification_agent',\n        name: 'Verification Agent',\n        description: 'Verifying information on websites, fact-checking, and validating data accuracy.'\n    },\n    {\n        id: 'research_assistant',\n        name: 'Research Assistant',\n        description: 'Conducting web-based research, gathering information from multiple sources, and compiling research findings.'\n    },\n    {\n        id: 'shopping_assistant',\n        name: 'Shopping Assistant',\n        description: 'Helping with online shopping, price comparisons, product research, and e-commerce tasks.'\n    },\n    {\n        id: 'price_comparison',\n        name: 'Price Comparison',\n        description: 'Comparing prices across different websites, finding deals, and analyzing product pricing.'\n    },\n    {\n        id: 'fact_checker',\n        name: 'Fact Checker',\n        description: 'Verifying facts and information across multiple web sources, cross-referencing data for accuracy.'\n    },\n    {\n        id: 'task_executor',\n        name: 'Task Executor',\n        description: 'General task execution and automation, handling various web-based tasks and workflows.'\n    }\n];\nconst getRoleById = (id)=>{\n    return PREDEFINED_ROLES.find((role)=>role.id === id);\n};\nconst getRoleName = (id)=>{\n    return PREDEFINED_ROLES.find((role)=>role.id === id)?.name;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29uZmlnL3JvbGVzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQU1PLE1BQU1BLG1CQUEyQjtJQUN0QztRQUNFQyxJQUFJO1FBQ0pDLE1BQU07UUFDTkMsYUFBYTtJQUNmO0lBQ0E7UUFDRUYsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLGFBQWE7SUFDZjtJQUNBO1FBQ0VGLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxhQUFhO0lBQ2Y7SUFDQTtRQUNFRixJQUFJO1FBQ0pDLE1BQU07UUFDTkMsYUFBYTtJQUNmO0lBQ0E7UUFDRUYsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLGFBQWE7SUFDZjtJQUNBO1FBQ0VGLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxhQUFhO0lBQ2Y7SUFDQTtRQUNFRixJQUFJO1FBQ0pDLE1BQU07UUFDTkMsYUFBYTtJQUNmO0lBQ0E7UUFDRUYsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLGFBQWE7SUFDZjtJQUNBO1FBQ0VGLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxhQUFhO0lBQ2Y7SUFDQTtRQUNFRixJQUFJO1FBQ0pDLE1BQU07UUFDTkMsYUFBYTtJQUNmO0lBQ0E7UUFDRUYsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLGFBQWE7SUFDZjtJQUNBO1FBQ0VGLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxhQUFhO0lBQ2Y7SUFDQTtRQUNFRixJQUFJO1FBQ0pDLE1BQU07UUFDTkMsYUFBYTtJQUNmO0lBRUE7UUFDRUYsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLGFBQWE7SUFDZjtJQUNBO1FBQ0VGLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxhQUFhO0lBQ2Y7SUFDQTtRQUNFRixJQUFJO1FBQ0pDLE1BQU07UUFDTkMsYUFBYTtJQUNmO0lBQ0E7UUFDRUYsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLGFBQWE7SUFDZjtJQUNBO1FBQ0VGLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxhQUFhO0lBQ2Y7SUFDQTtRQUNFRixJQUFJO1FBQ0pDLE1BQU07UUFDTkMsYUFBYTtJQUNmO0lBQ0E7UUFDRUYsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLGFBQWE7SUFDZjtJQUNBO1FBQ0VGLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxhQUFhO0lBQ2Y7Q0FFRCxDQUFDO0FBRUssTUFBTUMsY0FBYyxDQUFDSDtJQUMxQixPQUFPRCxpQkFBaUJLLElBQUksQ0FBQ0MsQ0FBQUEsT0FBUUEsS0FBS0wsRUFBRSxLQUFLQTtBQUNuRCxFQUFFO0FBRUssTUFBTU0sY0FBYyxDQUFDTjtJQUMxQixPQUFPRCxpQkFBaUJLLElBQUksQ0FBQ0MsQ0FBQUEsT0FBUUEsS0FBS0wsRUFBRSxLQUFLQSxLQUFLQztBQUN4RCxFQUFFIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcY29uZmlnXFxyb2xlcy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgaW50ZXJmYWNlIFJvbGUge1xyXG4gIGlkOiBzdHJpbmc7IC8vIGUuZy4sICdsb2dpYycsICdjb3B5d3JpdGluZydcclxuICBuYW1lOiBzdHJpbmc7IC8vIGUuZy4sICdMb2dpYyAmIFJlYXNvbmluZycsICdDb3B5d3JpdGluZyAmIENvbnRlbnQgQ3JlYXRpb24nXHJcbiAgZGVzY3JpcHRpb24/OiBzdHJpbmc7IC8vIE9wdGlvbmFsOiBBIGJyaWVmIGV4cGxhbmF0aW9uIG9mIHRoZSByb2xlXHJcbn1cclxuXHJcbmV4cG9ydCBjb25zdCBQUkVERUZJTkVEX1JPTEVTOiBSb2xlW10gPSBbXHJcbiAge1xyXG4gICAgaWQ6ICdnZW5lcmFsX2NoYXQnLFxyXG4gICAgbmFtZTogJ0dlbmVyYWwgQ2hhdCcsXHJcbiAgICBkZXNjcmlwdGlvbjogJ0Nhc3VhbCBjb252ZXJzYXRpb24sIHNpbXBsZSBxdWVzdGlvbnMsIGdyZWV0aW5ncywgYW5kIGJhc2ljIGludGVyYWN0aW9ucyB0aGF0IGRvIG5vdCByZXF1aXJlIHNwZWNpYWxpemVkIGV4cGVydGlzZS4nXHJcbiAgfSxcclxuICB7XHJcbiAgICBpZDogJ2xvZ2ljX3JlYXNvbmluZycsXHJcbiAgICBuYW1lOiAnTG9naWMgJiBSZWFzb25pbmcnLFxyXG4gICAgZGVzY3JpcHRpb246ICdNYXRoZW1hdGljYWwgY2FsY3VsYXRpb25zLCBsb2dpY2FsIHB1enpsZXMsIGFuYWx5dGljYWwgcHJvYmxlbS1zb2x2aW5nLCBkZWR1Y3RpdmUgcmVhc29uaW5nLCBhbmQgY29tcGxleCBhbmFseXRpY2FsIHRoaW5raW5nLidcclxuICB9LFxyXG4gIHtcclxuICAgIGlkOiAnd3JpdGluZycsXHJcbiAgICBuYW1lOiAnV3JpdGluZyAmIENvbnRlbnQgQ3JlYXRpb24nLFxyXG4gICAgZGVzY3JpcHRpb246ICdDcmVhdGluZyB3cml0dGVuIGNvbnRlbnQgbGlrZSBzdG9yaWVzLCBhcnRpY2xlcywgYm9va3MsIHBvZW1zLCBzY3JpcHRzLCBtYXJrZXRpbmcgY29weSwgYmxvZyBwb3N0cywgYW5kIGNyZWF0aXZlIG5hcnJhdGl2ZXMuJ1xyXG4gIH0sXHJcbiAge1xyXG4gICAgaWQ6ICdjb2RpbmdfZnJvbnRlbmQnLFxyXG4gICAgbmFtZTogJ0NvZGluZyAtIEZyb250ZW5kJyxcclxuICAgIGRlc2NyaXB0aW9uOiAnQnVpbGRpbmcgdXNlciBpbnRlcmZhY2VzIHdpdGggSFRNTCwgQ1NTLCBKYXZhU2NyaXB0LCBSZWFjdCwgVnVlLCBBbmd1bGFyLCB3ZWIgZGVzaWduLCBhbmQgY2xpZW50LXNpZGUgZGV2ZWxvcG1lbnQuJ1xyXG4gIH0sXHJcbiAge1xyXG4gICAgaWQ6ICdjb2RpbmdfYmFja2VuZCcsXHJcbiAgICBuYW1lOiAnQ29kaW5nIC0gQmFja2VuZCcsXHJcbiAgICBkZXNjcmlwdGlvbjogJ1Byb2dyYW1taW5nIHNlcnZlci1zaWRlIGFwcGxpY2F0aW9ucywgQVBJcywgZGF0YWJhc2VzLCBQeXRob24gc2NyaXB0cywgTm9kZS5qcywgSmF2YSwgYmFja2VuZCBsb2dpYywgYW5kIHN5c3RlbSBhcmNoaXRlY3R1cmUuJ1xyXG4gIH0sXHJcbiAge1xyXG4gICAgaWQ6ICdyZXNlYXJjaF9zeW50aGVzaXMnLFxyXG4gICAgbmFtZTogJ1Jlc2VhcmNoICYgU3ludGhlc2lzJyxcclxuICAgIGRlc2NyaXB0aW9uOiAnR2F0aGVyaW5nIGluZm9ybWF0aW9uIGZyb20gc291cmNlcywgY29uZHVjdGluZyByZXNlYXJjaCwgYW5hbHl6aW5nIGRhdGEsIGFuZCBzeW50aGVzaXppbmcgZmluZGluZ3MgaW50byBjb21wcmVoZW5zaXZlIHJlcG9ydHMuJ1xyXG4gIH0sXHJcbiAge1xyXG4gICAgaWQ6ICdzdW1tYXJpemF0aW9uX2JyaWVmaW5nJyxcclxuICAgIG5hbWU6ICdTdW1tYXJpemF0aW9uICYgQnJpZWZpbmcnLFxyXG4gICAgZGVzY3JpcHRpb246ICdDb25kZW5zaW5nIGxlbmd0aHkgZG9jdW1lbnRzLCBleHRyYWN0aW5nIGtleSBwb2ludHMsIGNyZWF0aW5nIGV4ZWN1dGl2ZSBzdW1tYXJpZXMsIGFuZCBicmllZmluZyBjb21wbGV4IGluZm9ybWF0aW9uLidcclxuICB9LFxyXG4gIHtcclxuICAgIGlkOiAndHJhbnNsYXRpb25fbG9jYWxpemF0aW9uJyxcclxuICAgIG5hbWU6ICdUcmFuc2xhdGlvbiAmIExvY2FsaXphdGlvbicsXHJcbiAgICBkZXNjcmlwdGlvbjogJ0NvbnZlcnRpbmcgdGV4dCBiZXR3ZWVuIGRpZmZlcmVudCBsYW5ndWFnZXMsIGxpbmd1aXN0aWMgdHJhbnNsYXRpb24sIGN1bHR1cmFsIGFkYXB0YXRpb24sIGFuZCBtdWx0aWxpbmd1YWwgY29tbXVuaWNhdGlvbi4nXHJcbiAgfSxcclxuICB7XHJcbiAgICBpZDogJ2RhdGFfZXh0cmFjdGlvbl9zdHJ1Y3R1cmluZycsXHJcbiAgICBuYW1lOiAnRGF0YSBFeHRyYWN0aW9uICYgU3RydWN0dXJpbmcnLFxyXG4gICAgZGVzY3JpcHRpb246ICdFeHRyYWN0aW5nIHNwZWNpZmljIGluZm9ybWF0aW9uIGZyb20gdW5zdHJ1Y3R1cmVkIHRleHQsIG9yZ2FuaXppbmcgZGF0YSBpbnRvIHRhYmxlcywgbGlzdHMsIEpTT04sIENTViwgYW5kIHN0cnVjdHVyZWQgZm9ybWF0cy4nXHJcbiAgfSxcclxuICB7XHJcbiAgICBpZDogJ2JyYWluc3Rvcm1pbmdfaWRlYXRpb24nLFxyXG4gICAgbmFtZTogJ0JyYWluc3Rvcm1pbmcgJiBJZGVhdGlvbicsXHJcbiAgICBkZXNjcmlwdGlvbjogJ0dlbmVyYXRpbmcgY3JlYXRpdmUgaWRlYXMsIGlubm92YXRpdmUgY29uY2VwdHMsIGJyYWluc3Rvcm1pbmcgc29sdXRpb25zLCBleHBsb3JpbmcgcG9zc2liaWxpdGllcywgYW5kIGNyZWF0aXZlIHRoaW5raW5nIHNlc3Npb25zLidcclxuICB9LFxyXG4gIHtcclxuICAgIGlkOiAnZWR1Y2F0aW9uX3R1dG9yaW5nJyxcclxuICAgIG5hbWU6ICdFZHVjYXRpb24gJiBUdXRvcmluZycsXHJcbiAgICBkZXNjcmlwdGlvbjogJ1RlYWNoaW5nIGNvbmNlcHRzLCBleHBsYWluaW5nIHRvcGljcyBzdGVwLWJ5LXN0ZXAsIGNyZWF0aW5nIGVkdWNhdGlvbmFsIG1hdGVyaWFscywgdHV0b3JpbmcsIGFuZCBwcm92aWRpbmcgbGVhcm5pbmcgZ3VpZGFuY2UuJ1xyXG4gIH0sXHJcbiAge1xyXG4gICAgaWQ6ICdpbWFnZV9nZW5lcmF0aW9uJyxcclxuICAgIG5hbWU6ICdJbWFnZSBHZW5lcmF0aW9uJyxcclxuICAgIGRlc2NyaXB0aW9uOiAnQ3JlYXRpbmcgdmlzdWFsIGltYWdlcywgYXJ0d29yaywgZ3JhcGhpY3MsIGFuZCBpbGx1c3RyYXRpb25zIGZyb20gdGV4dHVhbCBkZXNjcmlwdGlvbnMgdXNpbmcgQUkgaW1hZ2UgZ2VuZXJhdGlvbiBtb2RlbHMuJ1xyXG4gIH0sXHJcbiAge1xyXG4gICAgaWQ6ICdhdWRpb190cmFuc2NyaXB0aW9uJyxcclxuICAgIG5hbWU6ICdBdWRpbyBUcmFuc2NyaXB0aW9uJyxcclxuICAgIGRlc2NyaXB0aW9uOiAnQ29udmVydGluZyBzcGVlY2ggZnJvbSBhdWRpbyBmaWxlcyBpbnRvIHdyaXR0ZW4gdGV4dCwgdHJhbnNjcmliaW5nIHJlY29yZGluZ3MsIGFuZCBzcGVlY2gtdG8tdGV4dCBwcm9jZXNzaW5nLidcclxuICB9LFxyXG5cclxuICB7XHJcbiAgICBpZDogJ2RhdGFfZXh0cmFjdG9yJyxcclxuICAgIG5hbWU6ICdEYXRhIEV4dHJhY3RvcicsXHJcbiAgICBkZXNjcmlwdGlvbjogJ0V4dHJhY3Rpbmcgc3BlY2lmaWMgZGF0YSBmcm9tIHdlYiBwYWdlcywgc2NyYXBpbmcgY29udGVudCwgYW5kIGdhdGhlcmluZyBpbmZvcm1hdGlvbiBmcm9tIHdlYnNpdGVzLidcclxuICB9LFxyXG4gIHtcclxuICAgIGlkOiAnZm9ybV9maWxsZXInLFxyXG4gICAgbmFtZTogJ0Zvcm0gRmlsbGVyJyxcclxuICAgIGRlc2NyaXB0aW9uOiAnRmlsbGluZyBvdXQgd2ViIGZvcm1zLCBzdWJtaXR0aW5nIGRhdGEsIGFuZCBoYW5kbGluZyBmb3JtLWJhc2VkIGludGVyYWN0aW9ucyBvbiB3ZWJzaXRlcy4nXHJcbiAgfSxcclxuICB7XHJcbiAgICBpZDogJ3ZlcmlmaWNhdGlvbl9hZ2VudCcsXHJcbiAgICBuYW1lOiAnVmVyaWZpY2F0aW9uIEFnZW50JyxcclxuICAgIGRlc2NyaXB0aW9uOiAnVmVyaWZ5aW5nIGluZm9ybWF0aW9uIG9uIHdlYnNpdGVzLCBmYWN0LWNoZWNraW5nLCBhbmQgdmFsaWRhdGluZyBkYXRhIGFjY3VyYWN5LidcclxuICB9LFxyXG4gIHtcclxuICAgIGlkOiAncmVzZWFyY2hfYXNzaXN0YW50JyxcclxuICAgIG5hbWU6ICdSZXNlYXJjaCBBc3Npc3RhbnQnLFxyXG4gICAgZGVzY3JpcHRpb246ICdDb25kdWN0aW5nIHdlYi1iYXNlZCByZXNlYXJjaCwgZ2F0aGVyaW5nIGluZm9ybWF0aW9uIGZyb20gbXVsdGlwbGUgc291cmNlcywgYW5kIGNvbXBpbGluZyByZXNlYXJjaCBmaW5kaW5ncy4nXHJcbiAgfSxcclxuICB7XHJcbiAgICBpZDogJ3Nob3BwaW5nX2Fzc2lzdGFudCcsXHJcbiAgICBuYW1lOiAnU2hvcHBpbmcgQXNzaXN0YW50JyxcclxuICAgIGRlc2NyaXB0aW9uOiAnSGVscGluZyB3aXRoIG9ubGluZSBzaG9wcGluZywgcHJpY2UgY29tcGFyaXNvbnMsIHByb2R1Y3QgcmVzZWFyY2gsIGFuZCBlLWNvbW1lcmNlIHRhc2tzLidcclxuICB9LFxyXG4gIHtcclxuICAgIGlkOiAncHJpY2VfY29tcGFyaXNvbicsXHJcbiAgICBuYW1lOiAnUHJpY2UgQ29tcGFyaXNvbicsXHJcbiAgICBkZXNjcmlwdGlvbjogJ0NvbXBhcmluZyBwcmljZXMgYWNyb3NzIGRpZmZlcmVudCB3ZWJzaXRlcywgZmluZGluZyBkZWFscywgYW5kIGFuYWx5emluZyBwcm9kdWN0IHByaWNpbmcuJ1xyXG4gIH0sXHJcbiAge1xyXG4gICAgaWQ6ICdmYWN0X2NoZWNrZXInLFxyXG4gICAgbmFtZTogJ0ZhY3QgQ2hlY2tlcicsXHJcbiAgICBkZXNjcmlwdGlvbjogJ1ZlcmlmeWluZyBmYWN0cyBhbmQgaW5mb3JtYXRpb24gYWNyb3NzIG11bHRpcGxlIHdlYiBzb3VyY2VzLCBjcm9zcy1yZWZlcmVuY2luZyBkYXRhIGZvciBhY2N1cmFjeS4nXHJcbiAgfSxcclxuICB7XHJcbiAgICBpZDogJ3Rhc2tfZXhlY3V0b3InLFxyXG4gICAgbmFtZTogJ1Rhc2sgRXhlY3V0b3InLFxyXG4gICAgZGVzY3JpcHRpb246ICdHZW5lcmFsIHRhc2sgZXhlY3V0aW9uIGFuZCBhdXRvbWF0aW9uLCBoYW5kbGluZyB2YXJpb3VzIHdlYi1iYXNlZCB0YXNrcyBhbmQgd29ya2Zsb3dzLidcclxuICB9XHJcbiAgLy8gVE9ETzogQ29uc2lkZXIgYWRkaW5nIG1vcmUgc3BlY2lhbGl6ZWQgcm9sZXMgZm9yIGxlZ2FsLCBmaW5hbmNpYWwsIG9yIHNwZWNpZmljIGluZHVzdHJ5IHRhc2tzIGlmIG5lZWRlZC5cclxuXTtcclxuXHJcbmV4cG9ydCBjb25zdCBnZXRSb2xlQnlJZCA9IChpZDogc3RyaW5nKTogUm9sZSB8IHVuZGVmaW5lZCA9PiB7XHJcbiAgcmV0dXJuIFBSRURFRklORURfUk9MRVMuZmluZChyb2xlID0+IHJvbGUuaWQgPT09IGlkKTtcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCBnZXRSb2xlTmFtZSA9IChpZDogc3RyaW5nKTogc3RyaW5nIHwgdW5kZWZpbmVkID0+IHtcclxuICByZXR1cm4gUFJFREVGSU5FRF9ST0xFUy5maW5kKHJvbGUgPT4gcm9sZS5pZCA9PT0gaWQpPy5uYW1lO1xyXG59OyAiXSwibmFtZXMiOlsiUFJFREVGSU5FRF9ST0xFUyIsImlkIiwibmFtZSIsImRlc2NyaXB0aW9uIiwiZ2V0Um9sZUJ5SWQiLCJmaW5kIiwicm9sZSIsImdldFJvbGVOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/config/roles.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe-client.ts":
/*!**********************************!*\
  !*** ./src/lib/stripe-client.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TIER_CONFIGS: () => (/* binding */ TIER_CONFIGS),\n/* harmony export */   canPerformAction: () => (/* binding */ canPerformAction),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   getPriceIdForTier: () => (/* binding */ getPriceIdForTier),\n/* harmony export */   getTierConfig: () => (/* binding */ getTierConfig),\n/* harmony export */   getTierFromPriceId: () => (/* binding */ getTierFromPriceId),\n/* harmony export */   hasFeatureAccess: () => (/* binding */ hasFeatureAccess)\n/* harmony export */ });\n/* harmony import */ var _stripe_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./stripe-config */ \"(rsc)/./src/lib/stripe-config.ts\");\n// Client-safe Stripe utilities (no server-side Stripe instance)\n\nconst TIER_CONFIGS = {\n    free: {\n        name: 'Free',\n        price: '$0',\n        priceId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRICE_IDS.FREE,\n        productId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRODUCT_IDS.FREE,\n        features: [\n            'Unlimited API requests',\n            '1 Custom Configuration',\n            '3 API Keys per config',\n            'All 300+ AI models',\n            'Strict fallback routing only',\n            'Basic analytics only',\n            'No custom roles, basic router only',\n            'Limited logs',\n            'Community support'\n        ],\n        limits: {\n            configurations: 1,\n            apiKeysPerConfig: 3,\n            apiRequests: 999999,\n            canUseAdvancedRouting: false,\n            canUseCustomRoles: false,\n            maxCustomRoles: 0,\n            canUsePromptEngineering: false,\n            canUseKnowledgeBase: false,\n            knowledgeBaseDocuments: 0,\n            canUseSemanticCaching: false\n        }\n    },\n    starter: {\n        name: 'Starter',\n        price: '$19',\n        priceId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRICE_IDS.STARTER,\n        productId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRODUCT_IDS.STARTER,\n        features: [\n            'Unlimited API requests',\n            '4 Custom Configurations',\n            '5 API Keys per config',\n            'All 300+ AI models',\n            'Strict fallback + Complex routing (1 config limit)',\n            'Up to 3 custom roles',\n            'Intelligent role routing (1 config)',\n            'Prompt engineering (no file upload)',\n            'Enhanced logs and analytics',\n            'Community support'\n        ],\n        limits: {\n            configurations: 4,\n            apiKeysPerConfig: 5,\n            apiRequests: 999999,\n            canUseAdvancedRouting: true,\n            canUseCustomRoles: true,\n            maxCustomRoles: 3,\n            canUsePromptEngineering: true,\n            canUseKnowledgeBase: false,\n            knowledgeBaseDocuments: 0,\n            canUseSemanticCaching: false\n        }\n    },\n    professional: {\n        name: 'Professional',\n        price: '$49',\n        priceId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRICE_IDS.PROFESSIONAL,\n        productId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRODUCT_IDS.PROFESSIONAL,\n        features: [\n            'Unlimited API requests',\n            '20 Custom Configurations',\n            '15 API Keys per config',\n            'All 300+ AI models',\n            'All advanced routing strategies',\n            'Unlimited custom roles',\n            'Prompt engineering + Knowledge base (5 documents)',\n            'Semantic caching',\n            'Advanced analytics and logging',\n            'Priority email support'\n        ],\n        limits: {\n            configurations: 20,\n            apiKeysPerConfig: 15,\n            apiRequests: 999999,\n            canUseAdvancedRouting: true,\n            canUseCustomRoles: true,\n            maxCustomRoles: 999999,\n            canUsePromptEngineering: true,\n            canUseKnowledgeBase: true,\n            knowledgeBaseDocuments: 5,\n            canUseSemanticCaching: true\n        }\n    },\n    enterprise: {\n        name: 'Enterprise',\n        price: '$149',\n        priceId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRICE_IDS.ENTERPRISE,\n        productId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRODUCT_IDS.ENTERPRISE,\n        features: [\n            'Unlimited API requests',\n            'Unlimited configurations',\n            'Unlimited API keys',\n            'All 300+ models + priority access',\n            'All routing strategies',\n            'Unlimited custom roles',\n            'All features + priority support',\n            'Unlimited knowledge base documents',\n            'Advanced semantic caching',\n            'Custom integrations',\n            'Dedicated support + phone',\n            'SLA guarantee'\n        ],\n        limits: {\n            configurations: 999999,\n            apiKeysPerConfig: 999999,\n            apiRequests: 999999,\n            canUseAdvancedRouting: true,\n            canUseCustomRoles: true,\n            maxCustomRoles: 999999,\n            canUsePromptEngineering: true,\n            canUseKnowledgeBase: true,\n            knowledgeBaseDocuments: 999999,\n            canUseSemanticCaching: true\n        }\n    }\n};\nfunction getTierConfig(tier) {\n    return TIER_CONFIGS[tier];\n}\nfunction getPriceIdForTier(tier) {\n    return TIER_CONFIGS[tier].priceId;\n}\nfunction getTierFromPriceId(priceId) {\n    for (const [tier, config] of Object.entries(TIER_CONFIGS)){\n        if (config.priceId === priceId) {\n            return tier;\n        }\n    }\n    return 'free'; // Default fallback to free tier\n}\nfunction formatPrice(tier) {\n    return TIER_CONFIGS[tier].price;\n}\nfunction canPerformAction(tier, action, currentCount) {\n    const limits = TIER_CONFIGS[tier].limits;\n    switch(action){\n        case 'create_config':\n            return currentCount < limits.configurations;\n        case 'create_api_key':\n            return currentCount < limits.apiKeysPerConfig;\n        default:\n            return true;\n    }\n}\nfunction hasFeatureAccess(tier, feature) {\n    const limits = TIER_CONFIGS[tier].limits;\n    switch(feature){\n        case 'custom_roles':\n            return limits.canUseCustomRoles;\n        case 'knowledge_base':\n            return limits.canUseKnowledgeBase;\n        case 'advanced_routing':\n            return limits.canUseAdvancedRouting;\n        case 'prompt_engineering':\n            return limits.canUsePromptEngineering;\n        case 'semantic_caching':\n            return limits.canUseSemanticCaching;\n        case 'configurations':\n            return limits.configurations > 0; // All tiers can create at least some configurations\n        default:\n            return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe-client.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe-config.ts":
/*!**********************************!*\
  !*** ./src/lib/stripe-config.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PUBLIC_STRIPE_PRICE_IDS: () => (/* binding */ PUBLIC_STRIPE_PRICE_IDS),\n/* harmony export */   STRIPE_ENV_INFO: () => (/* binding */ STRIPE_ENV_INFO),\n/* harmony export */   STRIPE_KEYS: () => (/* binding */ STRIPE_KEYS),\n/* harmony export */   STRIPE_PRICE_IDS: () => (/* binding */ STRIPE_PRICE_IDS),\n/* harmony export */   STRIPE_PRODUCT_IDS: () => (/* binding */ STRIPE_PRODUCT_IDS)\n/* harmony export */ });\n// Stripe Configuration with Environment Detection\n// Automatically switches between test and live keys based on environment\nconst isProduction = \"development\" === 'production';\n// Stripe Keys - Auto-selected based on environment\nconst STRIPE_KEYS = {\n    publishableKey: isProduction ? process.env.STRIPE_LIVE_PUBLISHABLE_KEY : process.env.STRIPE_TEST_PUBLISHABLE_KEY,\n    secretKey: isProduction ? process.env.STRIPE_LIVE_SECRET_KEY : process.env.STRIPE_TEST_SECRET_KEY,\n    webhookSecret: process.env.STRIPE_WEBHOOK_SECRET\n};\n// Stripe Price IDs - Auto-selected based on environment\nconst STRIPE_PRICE_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRICE_ID : process.env.STRIPE_TEST_FREE_PRICE_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRICE_ID : process.env.STRIPE_TEST_STARTER_PRICE_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRICE_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID : process.env.STRIPE_TEST_ENTERPRISE_PRICE_ID\n};\n// Stripe Product IDs - Auto-selected based on environment\nconst STRIPE_PRODUCT_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRODUCT_ID : process.env.STRIPE_TEST_FREE_PRODUCT_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRODUCT_ID : process.env.STRIPE_TEST_STARTER_PRODUCT_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRODUCT_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRODUCT_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRODUCT_ID : process.env.STRIPE_TEST_ENTERPRISE_PRODUCT_ID\n};\n// Public Price IDs for frontend (auto-selected based on environment)\nconst PUBLIC_STRIPE_PRICE_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRICE_ID : process.env.STRIPE_TEST_FREE_PRICE_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRICE_ID : process.env.STRIPE_TEST_STARTER_PRICE_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRICE_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID : process.env.STRIPE_TEST_ENTERPRISE_PRICE_ID\n};\n// Environment info for debugging\nconst STRIPE_ENV_INFO = {\n    isProduction,\n    environment: isProduction ? 'LIVE' : 'TEST',\n    keysUsed: {\n        publishable: STRIPE_KEYS.publishableKey ? STRIPE_KEYS.publishableKey.substring(0, 20) + '...' : 'undefined',\n        secret: STRIPE_KEYS.secretKey ? STRIPE_KEYS.secretKey.substring(0, 20) + '...' : 'undefined'\n    }\n};\n// Log environment info in development\nif (!isProduction) {\n    console.log('🔧 Stripe Environment:', STRIPE_ENV_INFO.environment);\n    console.log('🔑 Using keys:', STRIPE_ENV_INFO.keysUsed);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe-config.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkeys%2F%5BapiKeyId%5D%2Froles%2Froute&page=%2Fapi%2Fkeys%2F%5BapiKeyId%5D%2Froles%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkeys%2F%5BapiKeyId%5D%2Froles%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();