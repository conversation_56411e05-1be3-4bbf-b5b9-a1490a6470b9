'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  UserIcon,
  KeyIcon,
  ShieldCheckIcon,
  BellIcon,
  EyeIcon,
  EyeSlashIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { createSupabaseBrowserClient } from '@/lib/supabase/client';
import { useSubscription } from '@/hooks/useSubscription';

export default function SettingsPage() {
  const router = useRouter();
  const { user } = useSubscription();
  const supabase = createSupabaseBrowserClient();

  const [activeSection, setActiveSection] = useState('account');
  const [loading, setLoading] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const [profileData, setProfileData] = useState({
    firstName: '',
    lastName: '',
    email: ''
  });

  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    securityAlerts: true,
    usageAlerts: true,
    marketingEmails: false
  });

  const sidebarItems = [
    { id: 'account', label: 'Account settings', icon: UserIcon },
    { id: 'notifications', label: 'Notifications', icon: BellIcon },
    { id: 'security', label: 'Security', icon: ShieldCheckIcon },
    { id: 'danger', label: 'Danger zone', icon: TrashIcon }
  ];

  // Load user data
  useEffect(() => {
    if (user) {
      setProfileData({
        firstName: user.user_metadata?.first_name || '',
        lastName: user.user_metadata?.last_name || '',
        email: user.email || ''
      });
    }
  }, [user]);

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const { error } = await supabase.auth.updateUser({
        data: {
          first_name: profileData.firstName,
          last_name: profileData.lastName
        }
      });

      if (error) throw error;

      toast.success('Profile updated successfully');
    } catch (error: any) {
      console.error('Profile update error:', error);
      toast.error(error.message || 'Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast.error('New passwords do not match');
      return;
    }

    if (passwordData.newPassword.length < 8) {
      toast.error('Password must be at least 8 characters long');
      return;
    }

    setLoading(true);

    try {
      const { error } = await supabase.auth.updateUser({
        password: passwordData.newPassword
      });

      if (error) throw error;

      toast.success('Password updated successfully');
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
    } catch (error: any) {
      console.error('Password update error:', error);
      toast.error(error.message || 'Failed to update password');
    } finally {
      setLoading(false);
    }
  };

  const handleNotificationUpdate = async () => {
    setLoading(true);
    
    try {
      // Here you would typically save to your database
      // For now, we'll just show a success message
      toast.success('Notification preferences updated');
    } catch (error: any) {
      console.error('Notification update error:', error);
      toast.error('Failed to update notification preferences');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <div className="flex">
          {/* Sidebar */}
          <div className="w-64 bg-white border-r border-gray-200 min-h-screen">
            <div className="p-6">
              <h1 className="text-2xl font-bold text-gray-900 mb-8">Settings</h1>
              <nav className="space-y-2">
                {sidebarItems.map((item) => {
                  const Icon = item.icon;
                  return (
                    <button
                      key={item.id}
                      onClick={() => setActiveSection(item.id)}
                      className={`w-full flex items-center gap-3 px-3 py-2 text-left rounded-lg transition-colors ${
                        activeSection === item.id
                          ? 'bg-orange-50 text-orange-700 border-r-2 border-orange-500'
                          : 'text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      <Icon className="h-5 w-5" />
                      {item.label}
                    </button>
                  );
                })}
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 p-8">
            {activeSection === 'account' && (
              <div className="max-w-2xl">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Account settings</h2>

                {/* Email Address Section */}
                <div className="mb-8">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Email address</h3>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-600">Your email address is <span className="font-medium text-gray-900">{profileData.email}</span></p>
                    </div>
                    <button className="text-blue-600 hover:text-blue-700 font-medium">
                      Change
                    </button>
                  </div>
                </div>

                {/* Profile Information */}
                <div className="mb-8">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Profile Information</h3>
                  <form onSubmit={handleProfileUpdate} className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="firstName" className="text-sm font-medium text-gray-700">First Name</Label>
                        <Input
                          id="firstName"
                          type="text"
                          value={profileData.firstName}
                          onChange={(e) => setProfileData(prev => ({ ...prev, firstName: e.target.value }))}
                          placeholder="Enter your first name"
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label htmlFor="lastName" className="text-sm font-medium text-gray-700">Last Name</Label>
                        <Input
                          id="lastName"
                          type="text"
                          value={profileData.lastName}
                          onChange={(e) => setProfileData(prev => ({ ...prev, lastName: e.target.value }))}
                          placeholder="Enter your last name"
                          className="mt-1"
                        />
                      </div>
                    </div>
                    <Button type="submit" disabled={loading} className="bg-gray-900 hover:bg-gray-800">
                      {loading ? 'Updating...' : 'Save changes'}
                    </Button>
                  </form>
                </div>

                {/* Password Section */}
                <div className="mb-8">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900">Password</h3>
                    <button className="text-blue-600 hover:text-blue-700 font-medium">
                      Hide
                    </button>
                  </div>
                  <form onSubmit={handlePasswordChange} className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="newPassword" className="text-sm font-medium text-gray-700">New password</Label>
                        <div className="relative mt-1">
                          <Input
                            id="newPassword"
                            type={showNewPassword ? "text" : "password"}
                            value={passwordData.newPassword}
                            onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
                            placeholder="••••••••••"
                            className="pr-10"
                          />
                          <button
                            type="button"
                            onClick={() => setShowNewPassword(!showNewPassword)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                          >
                            {showNewPassword ? <EyeSlashIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
                          </button>
                        </div>
                      </div>
                      <div>
                        <Label htmlFor="confirmPassword" className="text-sm font-medium text-gray-700">Current password</Label>
                        <div className="relative mt-1">
                          <Input
                            id="confirmPassword"
                            type={showConfirmPassword ? "text" : "password"}
                            value={passwordData.confirmPassword}
                            onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                            placeholder="••••••••••"
                            className="pr-10"
                          />
                          <button
                            type="button"
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                          >
                            {showConfirmPassword ? <EyeSlashIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
                          </button>
                        </div>
                      </div>
                    </div>
                    <div className="text-sm text-gray-600">
                      Can't remember your current password? <button type="button" className="text-blue-600 hover:text-blue-700">Reset your password</button>
                    </div>
                    <Button type="submit" disabled={loading} className="bg-gray-900 hover:bg-gray-800">
                      {loading ? 'Saving...' : 'Save password'}
                    </Button>
                  </form>
                </div>
              </div>
            )}

            {activeSection === 'notifications' && (
              <div className="max-w-2xl">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Notifications</h2>

                <div className="space-y-6">
                  {Object.entries(notificationSettings).map(([key, value]) => (
                    <div key={key} className="flex items-center justify-between py-4 border-b border-gray-200 last:border-b-0">
                      <div>
                        <h4 className="text-sm font-medium text-gray-900">
                          {key === 'emailNotifications' && 'Email Notifications'}
                          {key === 'securityAlerts' && 'Security Alerts'}
                          {key === 'usageAlerts' && 'Usage Alerts'}
                          {key === 'marketingEmails' && 'Marketing Emails'}
                        </h4>
                        <p className="text-sm text-gray-500 mt-1">
                          {key === 'emailNotifications' && 'Receive general email notifications'}
                          {key === 'securityAlerts' && 'Get notified about security events'}
                          {key === 'usageAlerts' && 'Alerts about API usage and limits'}
                          {key === 'marketingEmails' && 'Product updates and promotional content'}
                        </p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={value}
                          onChange={(e) => setNotificationSettings(prev => ({ ...prev, [key]: e.target.checked }))}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-500"></div>
                      </label>
                    </div>
                  ))}
                  <Button onClick={handleNotificationUpdate} disabled={loading} className="bg-gray-900 hover:bg-gray-800">
                    {loading ? 'Saving...' : 'Save preferences'}
                  </Button>
                </div>
              </div>
            )}

            {activeSection === 'security' && (
              <div className="max-w-2xl">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Security</h2>

                <div className="space-y-6">
                  <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200">
                    <div>
                      <h4 className="text-sm font-medium text-green-900">Account Security</h4>
                      <p className="text-sm text-green-700">Your account is secure and protected</p>
                    </div>
                    <ShieldCheckIcon className="h-6 w-6 text-green-600" />
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between py-3 border-b border-gray-200">
                      <div>
                        <h4 className="text-sm font-medium text-gray-900">Two-factor authentication</h4>
                        <p className="text-sm text-gray-500">Not enabled</p>
                      </div>
                      <Button variant="outline" size="sm">Enable</Button>
                    </div>

                    <div className="flex items-center justify-between py-3 border-b border-gray-200">
                      <div>
                        <h4 className="text-sm font-medium text-gray-900">Last password change</h4>
                        <p className="text-sm text-gray-500">Never</p>
                      </div>
                    </div>

                    <div className="flex items-center justify-between py-3">
                      <div>
                        <h4 className="text-sm font-medium text-gray-900">Account created</h4>
                        <p className="text-sm text-gray-500">
                          {user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'Unknown'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeSection === 'danger' && (
              <div className="max-w-2xl">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Danger zone</h2>

                <div className="border border-red-200 rounded-lg p-6 bg-red-50">
                  <h3 className="text-lg font-medium text-red-900 mb-2">Delete account</h3>
                  <p className="text-sm text-red-700 mb-4">
                    Would you like to delete your account?
                  </p>
                  <p className="text-sm text-red-600 mb-6">
                    This account contains {Math.floor(Math.random() * 100) + 1} configurations and API keys. Deleting your account will
                    remove all the content associated with it.
                  </p>
                  <Button
                    variant="outline"
                    className="text-red-600 border-red-300 hover:bg-red-100"
                    onClick={() => {
                      if (confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
                        toast.error('Account deletion is not yet implemented. Please contact support.');
                      }
                    }}
                  >
                    I want to delete my account
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
