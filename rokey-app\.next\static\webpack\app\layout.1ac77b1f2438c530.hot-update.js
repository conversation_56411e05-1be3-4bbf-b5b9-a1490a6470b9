"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6dd4ef50281f\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjZkZDRlZjUwMjgxZlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/GlobalSearch.tsx":
/*!*****************************************!*\
  !*** ./src/components/GlobalSearch.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalSearch: () => (/* binding */ GlobalSearch)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=MagnifyingGlassIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=MagnifyingGlassIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BeakerIcon_ChartBarIcon_CogIcon_DocumentTextIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BeakerIcon,ChartBarIcon,CogIcon,DocumentTextIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BeakerIcon_ChartBarIcon_CogIcon_DocumentTextIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BeakerIcon,ChartBarIcon,CogIcon,DocumentTextIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BeakerIcon_ChartBarIcon_CogIcon_DocumentTextIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BeakerIcon,ChartBarIcon,CogIcon,DocumentTextIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BeakerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BeakerIcon_ChartBarIcon_CogIcon_DocumentTextIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BeakerIcon,ChartBarIcon,CogIcon,DocumentTextIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BeakerIcon_ChartBarIcon_CogIcon_DocumentTextIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BeakerIcon,ChartBarIcon,CogIcon,DocumentTextIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BeakerIcon_ChartBarIcon_CogIcon_DocumentTextIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BeakerIcon,ChartBarIcon,CogIcon,DocumentTextIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* __next_internal_client_entry_do_not_use__ GlobalSearch auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction GlobalSearch(param) {\n    let { isOpen, onClose } = param;\n    _s();\n    const [query, setQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedIndex, setSelectedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Static page results\n    const staticPages = [\n        {\n            id: 'dashboard',\n            title: 'Dashboard',\n            subtitle: 'Overview & analytics',\n            type: 'page',\n            href: '/dashboard',\n            icon: _barrel_optimize_names_BeakerIcon_ChartBarIcon_CogIcon_DocumentTextIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n        },\n        {\n            id: 'my-models',\n            title: 'My Models',\n            subtitle: 'API key management',\n            type: 'page',\n            href: '/my-models',\n            icon: _barrel_optimize_names_BeakerIcon_ChartBarIcon_CogIcon_DocumentTextIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            id: 'playground',\n            title: 'Playground',\n            subtitle: 'Test your models',\n            type: 'page',\n            href: '/playground',\n            icon: _barrel_optimize_names_BeakerIcon_ChartBarIcon_CogIcon_DocumentTextIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            id: 'routing-setup',\n            title: 'Routing Setup',\n            subtitle: 'Configure routing',\n            type: 'page',\n            href: '/routing-setup',\n            icon: _barrel_optimize_names_BeakerIcon_ChartBarIcon_CogIcon_DocumentTextIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            id: 'logs',\n            title: 'Logs',\n            subtitle: 'Request history',\n            type: 'page',\n            href: '/logs',\n            icon: _barrel_optimize_names_BeakerIcon_ChartBarIcon_CogIcon_DocumentTextIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        }\n    ];\n    // Focus input when opened\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GlobalSearch.useEffect\": ()=>{\n            if (isOpen && inputRef.current) {\n                inputRef.current.focus();\n            }\n        }\n    }[\"GlobalSearch.useEffect\"], [\n        isOpen\n    ]);\n    // Search function\n    const performSearch = async (searchQuery)=>{\n        if (!searchQuery.trim()) {\n            setResults([]);\n            return;\n        }\n        setLoading(true);\n        const allResults = [];\n        try {\n            // Search static pages\n            const pageResults = staticPages.filter((page)=>page.title.toLowerCase().includes(searchQuery.toLowerCase()) || page.subtitle.toLowerCase().includes(searchQuery.toLowerCase()));\n            allResults.push(...pageResults);\n            // Search API configurations\n            let configResults = [];\n            try {\n                const configResponse = await fetch('/api/custom-configs');\n                if (configResponse.ok) {\n                    const configData = await configResponse.json();\n                    // The API returns data directly as an array, not wrapped in a configs property\n                    const configs = Array.isArray(configData) ? configData : [];\n                    configResults = configs.filter((config)=>{\n                        var _config_name;\n                        return config === null || config === void 0 ? void 0 : (_config_name = config.name) === null || _config_name === void 0 ? void 0 : _config_name.toLowerCase().includes(searchQuery.toLowerCase());\n                    }).map((config)=>({\n                            id: \"config-\".concat(config.id),\n                            title: config.name,\n                            subtitle: \"Configuration • \".concat(config.routing_strategy || 'Default'),\n                            type: 'config',\n                            href: \"/my-models/\".concat(config.id),\n                            icon: _barrel_optimize_names_BeakerIcon_ChartBarIcon_CogIcon_DocumentTextIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                            metadata: new Date(config.created_at).toLocaleDateString()\n                        }));\n                }\n            } catch (error) {\n                console.error('Error searching configurations:', error);\n            }\n            // Search user-generated API keys\n            let userKeyResults = [];\n            try {\n                const userKeysResponse = await fetch('/api/user-api-keys');\n                if (userKeysResponse.ok) {\n                    const userKeysData = await userKeysResponse.json();\n                    const apiKeys = userKeysData.api_keys || userKeysData || [];\n                    if (Array.isArray(apiKeys)) {\n                        userKeyResults = apiKeys.filter((key)=>{\n                            var _key_key_name;\n                            return key === null || key === void 0 ? void 0 : (_key_key_name = key.key_name) === null || _key_key_name === void 0 ? void 0 : _key_key_name.toLowerCase().includes(searchQuery.toLowerCase());\n                        }).map((key)=>({\n                                id: \"user-key-\".concat(key.id),\n                                title: key.key_name,\n                                subtitle: \"User API Key • \".concat(key.status || 'Unknown'),\n                                type: 'user-api-key',\n                                href: \"/my-models/\".concat(key.custom_api_config_id, \"?tab=user-api-keys\"),\n                                icon: _barrel_optimize_names_BeakerIcon_ChartBarIcon_CogIcon_DocumentTextIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                                metadata: \"\".concat(key.total_requests || 0, \" requests\")\n                            }));\n                    }\n                }\n            } catch (error) {\n                console.error('Error searching user API keys:', error);\n            }\n            // Add configuration and user key results to the main results array\n            allResults.push(...configResults, ...userKeyResults);\n            // Sort results by relevance (exact matches first, then partial matches)\n            const sortedResults = allResults.sort((a, b)=>{\n                const aExact = a.title.toLowerCase() === searchQuery.toLowerCase();\n                const bExact = b.title.toLowerCase() === searchQuery.toLowerCase();\n                if (aExact && !bExact) return -1;\n                if (!aExact && bExact) return 1;\n                return 0;\n            });\n            setResults(sortedResults.slice(0, 10)); // Limit to 10 results\n            setSelectedIndex(0);\n        } catch (error) {\n            console.error('Search error:', error);\n            setResults([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Debounced search\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GlobalSearch.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"GlobalSearch.useEffect.timer\": ()=>{\n                    performSearch(query);\n                }\n            }[\"GlobalSearch.useEffect.timer\"], 300);\n            return ({\n                \"GlobalSearch.useEffect\": ()=>clearTimeout(timer)\n            })[\"GlobalSearch.useEffect\"];\n        }\n    }[\"GlobalSearch.useEffect\"], [\n        query\n    ]);\n    // Handle keyboard navigation\n    const handleKeyDown = (e)=>{\n        switch(e.key){\n            case 'ArrowDown':\n                e.preventDefault();\n                setSelectedIndex((prev)=>Math.min(prev + 1, results.length - 1));\n                break;\n            case 'ArrowUp':\n                e.preventDefault();\n                setSelectedIndex((prev)=>Math.max(prev - 1, 0));\n                break;\n            case 'Enter':\n                e.preventDefault();\n                if (results[selectedIndex]) {\n                    handleResultClick(results[selectedIndex]);\n                }\n                break;\n            case 'Escape':\n                e.preventDefault();\n                onClose();\n                break;\n        }\n    };\n    const handleResultClick = (result)=>{\n        router.push(result.href);\n        onClose();\n        setQuery('');\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/20 backdrop-blur-sm z-50\",\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-20 left-1/2 transform -translate-x-1/2 w-full max-w-2xl mx-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-2xl border border-gray-200 overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center px-4 py-3 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-5 w-5 text-gray-400 mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    ref: inputRef,\n                                    type: \"text\",\n                                    placeholder: \"Search configurations, API keys, pages...\",\n                                    value: query,\n                                    onChange: (e)=>setQuery(e.target.value),\n                                    onKeyDown: handleKeyDown,\n                                    className: \"flex-1 text-gray-900 placeholder-gray-500 bg-transparent border-none outline-none text-sm\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"p-1 hover:bg-gray-100 rounded-lg transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-h-96 overflow-y-auto\",\n                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-8 text-center text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500 mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-sm\",\n                                        children: \"Searching...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 15\n                            }, this) : results.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"py-2\",\n                                children: results.map((result, index)=>{\n                                    const Icon = result.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleResultClick(result),\n                                        className: \"w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors flex items-center space-x-3 \".concat(index === selectedIndex ? 'bg-orange-50 border-r-2 border-orange-500' : ''),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"h-5 w-5 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-900 truncate\",\n                                                        children: result.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 truncate\",\n                                                        children: result.subtitle\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 23\n                                            }, this),\n                                            result.metadata && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: result.metadata\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 27\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, result.id, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 21\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 15\n                            }, this) : query.trim() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-8 text-center text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-8 w-8 mx-auto text-gray-300 mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: [\n                                            'No results found for \"',\n                                            query,\n                                            '\"'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-400 mt-1\",\n                                        children: 'Try searching for pages like \"dashboard\", \"playground\", or \"settings\"'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-8 text-center text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-8 w-8 mx-auto text-gray-300 mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: \"Start typing to search...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-400 mt-1\",\n                                        children: \"Search across configurations, API keys, and pages\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 bg-gray-50 border-t border-gray-200 text-xs text-gray-500 flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"↑↓ Navigate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"↵ Select\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Esc Close\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        results.length,\n                                        \" results\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalSearch.tsx\",\n                lineNumber: 232,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(GlobalSearch, \"SfO9/tirtWw2ms8PdYF/Xa+koQc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = GlobalSearch;\nvar _c;\n$RefreshReg$(_c, \"GlobalSearch\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/GlobalSearch.tsx\n"));

/***/ })

});